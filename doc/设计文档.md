B端智能助手-APA

#### 定位

浏览器客户端插件，与服务端相结合，在服务端实现RPA，做操作流程的自动化任务编排。

本质上是做应用产品，背景现状：

1）遗留系统开放度低，没有成熟的API

2）关联的系统领域多，改造开发成本高

3）大部分能力，现有web系统已经具备基本能力

4）现有系统操作体验、效率不佳


架构图

![1748312496701](images/设计文档/1748312496701.png)

#### 功能模块及名词解释

##### **目标代理系统（T-Sys）**：想要服务端代理自动化操作的WEB系统，例如：积理ERP、万商、海博等；

##### 客户端插件（C-Assistant）：Chrome浏览器插件，用Javascript实现，前端应用。

```
 职责：捕获用户会话（Cookies/Token）、注入监控脚本、转发数据
 技术栈：Chrome Extension API + JavaScript
```


| **组件/页面名称** | **前端样式描述**                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          | **原型图片参考**                                                   |
| ----------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------ |
| 悬浮图标          | 插件在特定T-Sys的域名URL被打开后，才会以侧边悬浮窗的形式显示，默认吸附在浏览器右侧，默认以“圆形机器人头像”样式展示。点击后会展开插件首页，再次点解会切换为收起状态；                                                                                                                                                                                                                                                                                                                                                                    | ![](https://apijoyspace.jd.com/v1/files/uhNYGLwamGzDDseTcI5o/link) |
| 插件首页          | 点击悬浮图标后展开为一个悬浮框网页，网页有3个置顶的tab，第一个tab是“辅助任务”、第二个是“智能对话”、第三个是“设置”；另外第一行靠右侧有一个与服务端连接状态展示的label“连接中/已连接/未连接/重试”，会根据跟服务端通信状态来动态展示，并且用绿、黄、红不同颜色代表不同的状态提醒；第一行靠左侧显示“智能助手”；挨着“智能助手”显示当前站点是否登录的状态“已登录/未获取到登录态”；点击“未获取到登录态”自动跳转到“SSO登录页面”；                                                                                               | ![](https://apijoyspace.jd.com/v1/files/LiyMoZ2GWkYZJwpX63Qv/link) |
| “辅助任务”tab   | 分“发起新任务”、“任务列表”两个子功能；“发起新任务”：点击按钮后，会转到一个新的子页面；子页面包含“任务类型”下拉选项，选项后台可配置；根据选择不同的选项，会联动显示不同的表单模板；模板默认为Excel文件模板，客户可以下载这个模板；然后可以填充Excel文件后，导入到系统中，导入成功则显示任务提交成功。“任务列表”：可查询已导入的任务列表信息，列字段包含“任务编号”、“任务名称”、“任务状态(待执行、执行中、成功/失败)”、“创建时间”、“查看任务详情”；查询条件包括“任务名称”、“创建时间(日历控件)”、“状态(下拉框)”； | ![](https://apijoyspace.jd.com/v1/files/IsD3zyWYmnSCb25uiLZv/link) |
| “智能对话”tab   | 聊天对话框形式，单聊形式；对话对象为“智能助手(机器人)”，另一方是当前登录用户；消息发送框，可以发快捷指令、文本消息，点击“发送”按钮后，消息即展示到上方的对话框；                                                                                                                                                                                                                                                                                                                                                                      | ![](https://apijoyspace.jd.com/v1/files/qR1HhA4ZiEZPrYZCX2El/link) |
| “设置”tab       | 包含设置项：服务器地址、代理站点、代理站点sso、是否桌面通知、是否声音提醒等；其中“代理站点”为下拉菜单，选项为提前服务端动态下发的枚举值；“代理站点”与“代理站点SSO”联动，构成键值对；点击“保存设置”提交至服务端；                                                                                                                                                                                                                                                                                                                  | ![](https://apijoyspace.jd.com/v1/files/ACRuBINyRK3OGXDCL4jR/link) |

客户端业务逻辑：


| **模块**   | **核心实现逻辑**                                                                                                                                                                                                                                                                                                |
| ---------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 登录       | 客户端插件，自动检测客户端登录态，检测逻辑是读取T-Sys对应的cookie，如果能取到特定的登录cookie值则认为已登录，进入正常插件首页；否则提示目标系统未登录，则显示“xxx代理系统未登录”、并露出“SSO登录”按钮，点击按钮跳转到提前配置好的T-Sys的登录URL，登录成功后插件会自动后台定时刷新下以判断是否“登录成功”。 |
| 通信协议   | 客户端与服务端采用WebSocket；数据传输格式用JSON;主要用于消息通知、聊天对话协议场景；                                                                                                                                                                                                                            |
| cookie采集 | 浏览器插件获取可读取当前站点cookie权限，自动采集当前站点对应的“代理站点SSO”，并自动后台异步提交到服务端；                                                                                                                                                                                                     |
| 任务       | 任务创建、任务查询，均采用HTTP协议与服务端通信；                                                                                                                                                                                                                                                                |
| 聊天对话   | 聊天逻辑参考行业常见的简易IM即时通讯软件逻辑；针对需要深度思考的答案，机器人可以采用大模型流式对话的形式；对话信息通过WebSocket从服务端获取；                                                                                                                                                                   |

##### 服务器端（S-Assitant）

```
   - 职责：接收任务、模拟请求、调度任务队列、返回结果
   - 技术栈：Java（aiohttp异步请求） + Redis（队列） + 代理IP池
```

#### 潜在问题

1）安全合规性？

2）技术门槛低？

3）服务端多个cookies并发执行会不会有问题？

4）任务与操作步骤如何串联关联，中途有卡点的如何与人工交互？
