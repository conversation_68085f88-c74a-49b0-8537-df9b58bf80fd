// Cookie管理器
class CookieManager {
  constructor() {
    this.loginCookieNames = [
      'session',
      'sessionid',
      'session_id',
      'token',
      'auth_token',
      'access_token',
      'jwt',
      'jsessionid',
      'phpsessid',
      'asp.net_sessionid',
      'laravel_session',
      'connect.sid',
      'user',
      'userid',
      'user_id',
      'login',
      'logged_in',
      'authenticated',
      'auth'
    ];
    
    this.cookieCache = new Map();
    this.lastUpdateTime = new Map();
    this.cacheTimeout = 60000; // 1分钟缓存
  }

  // 获取指定域名的所有Cookie
  async getCookies(domain) {
    try {
      // 检查缓存
      const cacheKey = domain;
      const cached = this.cookieCache.get(cacheKey);
      const lastUpdate = this.lastUpdateTime.get(cacheKey);
      
      if (cached && lastUpdate && (Date.now() - lastUpdate) < this.cacheTimeout) {
        return { success: true, cookies: cached };
      }

      // 从Chrome API获取Cookie
      const cookies = await chrome.cookies.getAll({ domain: domain });
      
      // 更新缓存
      this.cookieCache.set(cacheKey, cookies);
      this.lastUpdateTime.set(cacheKey, Date.now());
      
      console.log(`获取到${domain}的${cookies.length}个Cookie`);
      
      return { success: true, cookies: cookies };
    } catch (error) {
      console.error('获取Cookie失败:', error);
      return { success: false, error: error.message, cookies: [] };
    }
  }

  // 获取登录相关的Cookie
  async getLoginCookies(domain) {
    const result = await this.getCookies(domain);
    if (!result.success) {
      return result;
    }

    const loginCookies = result.cookies.filter(cookie => 
      this.isLoginCookie(cookie.name)
    );

    return {
      success: true,
      cookies: loginCookies,
      hasLoginCookies: loginCookies.length > 0
    };
  }

  // 检查Cookie名称是否为登录相关
  isLoginCookie(cookieName) {
    const name = cookieName.toLowerCase();
    return this.loginCookieNames.some(loginName => 
      name.includes(loginName) || loginName.includes(name)
    );
  }

  // 检查域名是否已登录
  async checkLoginStatus(domain) {
    try {
      const result = await this.getLoginCookies(domain);
      if (!result.success) {
        return { success: false, isLoggedIn: false, error: result.error };
      }

      const hasValidLoginCookies = result.cookies.some(cookie => 
        this.isValidLoginCookie(cookie)
      );

      return {
        success: true,
        isLoggedIn: hasValidLoginCookies,
        loginCookies: result.cookies,
        cookieCount: result.cookies.length
      };
    } catch (error) {
      console.error('检查登录状态失败:', error);
      return { success: false, isLoggedIn: false, error: error.message };
    }
  }

  // 检查Cookie是否为有效的登录Cookie
  isValidLoginCookie(cookie) {
    // 检查Cookie是否过期
    if (cookie.expirationDate && cookie.expirationDate < Date.now() / 1000) {
      return false;
    }

    // 检查Cookie值是否有效（不为空且长度合理）
    if (!cookie.value || cookie.value.length < 3) {
      return false;
    }

    // 检查是否为明显的登录Cookie
    return this.isLoginCookie(cookie.name);
  }

  // 监听Cookie变化
  startCookieMonitoring(domain, callback) {
    const listener = (changeInfo) => {
      if (changeInfo.cookie.domain === domain || 
          changeInfo.cookie.domain === `.${domain}`) {
        
        // 清除缓存
        this.cookieCache.delete(domain);
        this.lastUpdateTime.delete(domain);
        
        console.log('Cookie变化:', changeInfo);
        
        if (callback) {
          callback(changeInfo);
        }
      }
    };

    chrome.cookies.onChanged.addListener(listener);
    
    return () => {
      chrome.cookies.onChanged.removeListener(listener);
    };
  }

  // 自动提交Cookie到服务器
  async submitCookiesToServer(domain, serverUrl) {
    try {
      const result = await this.getCookies(domain);
      if (!result.success) {
        throw new Error(result.error);
      }

      // 过滤敏感Cookie
      const safeCookies = result.cookies.map(cookie => ({
        name: cookie.name,
        value: cookie.value,
        domain: cookie.domain,
        path: cookie.path,
        secure: cookie.secure,
        httpOnly: cookie.httpOnly,
        expirationDate: cookie.expirationDate
      }));

      // 发送到服务器
      const response = await fetch(`${serverUrl}/api/cookies`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          domain: domain,
          cookies: safeCookies,
          timestamp: Date.now()
        })
      });

      if (!response.ok) {
        throw new Error(`服务器响应错误: ${response.status}`);
      }

      const responseData = await response.json();
      console.log('Cookie提交成功:', responseData);
      
      return { success: true, data: responseData };
    } catch (error) {
      console.error('提交Cookie失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 设置Cookie
  async setCookie(cookieDetails) {
    try {
      await chrome.cookies.set(cookieDetails);
      
      // 清除相关缓存
      this.cookieCache.delete(cookieDetails.domain);
      this.lastUpdateTime.delete(cookieDetails.domain);
      
      return { success: true };
    } catch (error) {
      console.error('设置Cookie失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 删除Cookie
  async removeCookie(url, name) {
    try {
      await chrome.cookies.remove({ url: url, name: name });
      
      // 清除相关缓存
      const domain = new URL(url).hostname;
      this.cookieCache.delete(domain);
      this.lastUpdateTime.delete(domain);
      
      return { success: true };
    } catch (error) {
      console.error('删除Cookie失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 清除指定域名的所有Cookie
  async clearDomainCookies(domain) {
    try {
      const result = await this.getCookies(domain);
      if (!result.success) {
        return result;
      }

      const removePromises = result.cookies.map(cookie => {
        const url = `http${cookie.secure ? 's' : ''}://${cookie.domain}${cookie.path}`;
        return chrome.cookies.remove({ url: url, name: cookie.name });
      });

      await Promise.all(removePromises);
      
      // 清除缓存
      this.cookieCache.delete(domain);
      this.lastUpdateTime.delete(domain);
      
      console.log(`已清除${domain}的所有Cookie`);
      return { success: true };
    } catch (error) {
      console.error('清除Cookie失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 导出Cookie为JSON格式
  async exportCookies(domain) {
    const result = await this.getCookies(domain);
    if (!result.success) {
      return result;
    }

    const exportData = {
      domain: domain,
      exportTime: new Date().toISOString(),
      cookies: result.cookies.map(cookie => ({
        name: cookie.name,
        value: cookie.value,
        domain: cookie.domain,
        path: cookie.path,
        secure: cookie.secure,
        httpOnly: cookie.httpOnly,
        expirationDate: cookie.expirationDate,
        sameSite: cookie.sameSite
      }))
    };

    return { success: true, data: exportData };
  }

  // 从JSON导入Cookie
  async importCookies(cookieData) {
    try {
      if (!cookieData.cookies || !Array.isArray(cookieData.cookies)) {
        throw new Error('无效的Cookie数据格式');
      }

      const setPromises = cookieData.cookies.map(cookie => {
        const url = `http${cookie.secure ? 's' : ''}://${cookie.domain}${cookie.path}`;
        return this.setCookie({
          url: url,
          name: cookie.name,
          value: cookie.value,
          domain: cookie.domain,
          path: cookie.path,
          secure: cookie.secure,
          httpOnly: cookie.httpOnly,
          expirationDate: cookie.expirationDate,
          sameSite: cookie.sameSite
        });
      });

      await Promise.all(setPromises);
      
      console.log(`已导入${cookieData.cookies.length}个Cookie`);
      return { success: true };
    } catch (error) {
      console.error('导入Cookie失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 获取Cookie统计信息
  async getCookieStats(domain) {
    const result = await this.getCookies(domain);
    if (!result.success) {
      return result;
    }

    const stats = {
      total: result.cookies.length,
      secure: result.cookies.filter(c => c.secure).length,
      httpOnly: result.cookies.filter(c => c.httpOnly).length,
      expired: result.cookies.filter(c => 
        c.expirationDate && c.expirationDate < Date.now() / 1000
      ).length,
      login: result.cookies.filter(c => this.isLoginCookie(c.name)).length
    };

    return { success: true, stats: stats };
  }

  // 清除缓存
  clearCache() {
    this.cookieCache.clear();
    this.lastUpdateTime.clear();
  }

  // 添加自定义登录Cookie名称
  addLoginCookieName(name) {
    if (!this.loginCookieNames.includes(name.toLowerCase())) {
      this.loginCookieNames.push(name.toLowerCase());
    }
  }

  // 移除自定义登录Cookie名称
  removeLoginCookieName(name) {
    const index = this.loginCookieNames.indexOf(name.toLowerCase());
    if (index > -1) {
      this.loginCookieNames.splice(index, 1);
    }
  }
}

// 创建全局Cookie管理器实例
const cookieManager = new CookieManager();

// 导出管理器
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CookieManager;
} else if (typeof window !== 'undefined') {
  window.CookieManager = CookieManager;
  window.cookieManager = cookieManager;
}
