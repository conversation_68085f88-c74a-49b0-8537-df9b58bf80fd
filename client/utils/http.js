// HTTP请求管理器
class HttpManager {
  constructor() {
    this.baseUrl = '';
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
    this.timeout = 30000; // 30秒超时
  }

  // 设置基础URL
  setBaseUrl(url) {
    this.baseUrl = url.replace(/\/$/, ''); // 移除末尾的斜杠
  }

  // 设置默认请求头
  setDefaultHeaders(headers) {
    this.defaultHeaders = { ...this.defaultHeaders, ...headers };
  }

  // 设置超时时间
  setTimeout(timeout) {
    this.timeout = timeout;
  }

  // 通用请求方法
  async request(url, options = {}) {
    const fullUrl = url.startsWith('http') ? url : `${this.baseUrl}${url}`;
    
    const config = {
      method: 'GET',
      headers: { ...this.defaultHeaders },
      ...options
    };

    // 合并请求头
    if (options.headers) {
      config.headers = { ...config.headers, ...options.headers };
    }

    // 处理请求体
    if (config.body && typeof config.body === 'object' && config.headers['Content-Type'] === 'application/json') {
      config.body = JSON.stringify(config.body);
    }

    try {
      console.log(`HTTP ${config.method} ${fullUrl}`, config);

      // 创建带超时的fetch请求
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(fullUrl, {
        ...config,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      // 检查响应状态
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 解析响应
      const contentType = response.headers.get('content-type');
      let data;

      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }

      console.log(`HTTP响应:`, data);

      return {
        success: true,
        data: data,
        status: response.status,
        headers: response.headers
      };

    } catch (error) {
      console.error(`HTTP请求失败:`, error);

      if (error.name === 'AbortError') {
        throw new Error('请求超时');
      }

      throw error;
    }
  }

  // GET请求
  async get(url, params = {}, options = {}) {
    // 构建查询参数
    const queryString = new URLSearchParams(params).toString();
    const fullUrl = queryString ? `${url}?${queryString}` : url;

    return this.request(fullUrl, {
      method: 'GET',
      ...options
    });
  }

  // POST请求
  async post(url, data = {}, options = {}) {
    return this.request(url, {
      method: 'POST',
      body: data,
      ...options
    });
  }

  // PUT请求
  async put(url, data = {}, options = {}) {
    return this.request(url, {
      method: 'PUT',
      body: data,
      ...options
    });
  }

  // DELETE请求
  async delete(url, options = {}) {
    return this.request(url, {
      method: 'DELETE',
      ...options
    });
  }

  // 上传文件
  async upload(url, file, options = {}) {
    const formData = new FormData();
    formData.append('file', file);

    // 添加额外的表单字段
    if (options.fields) {
      Object.keys(options.fields).forEach(key => {
        formData.append(key, options.fields[key]);
      });
    }

    return this.request(url, {
      method: 'POST',
      body: formData,
      headers: {
        // 不设置Content-Type，让浏览器自动设置multipart/form-data边界
        ...options.headers
      }
    });
  }

  // 任务相关API
  async createTask(taskData) {
    return this.post('/api/tasks', taskData);
  }

  async getTaskList(params = {}) {
    return this.get('/api/tasks', params);
  }

  async getTaskDetail(taskId) {
    return this.get(`/api/tasks/${taskId}`);
  }

  async updateTask(taskId, data) {
    return this.put(`/api/tasks/${taskId}`, data);
  }

  async deleteTask(taskId) {
    return this.delete(`/api/tasks/${taskId}`);
  }

  async uploadTaskFile(file, taskType) {
    return this.upload('/api/tasks/upload', file, {
      fields: { taskType }
    });
  }

  // 获取任务模板
  async getTaskTemplate(taskType) {
    return this.get(`/api/tasks/template/${taskType}`);
  }

  // 聊天相关API
  async sendChatMessage(message, sessionId) {
    return this.post('/api/chat/message', {
      message,
      sessionId,
      timestamp: Date.now()
    });
  }

  async getChatHistory(sessionId, params = {}) {
    return this.get(`/api/chat/history/${sessionId}`, params);
  }

  // 设置相关API
  async getSettings() {
    return this.get('/api/settings');
  }

  async updateSettings(settings) {
    return this.post('/api/settings', settings);
  }

  async getTargetSites() {
    return this.get('/api/settings/target-sites');
  }

  // 登录状态相关API
  async checkLoginStatus(domain, cookies) {
    return this.post('/api/auth/check', {
      domain,
      cookies
    });
  }

  async submitCookies(domain, cookies) {
    return this.post('/api/auth/cookies', {
      domain,
      cookies,
      timestamp: Date.now()
    });
  }

  // 系统状态API
  async getSystemStatus() {
    return this.get('/api/system/status');
  }

  async testConnection() {
    return this.get('/api/system/ping');
  }

  // 通知相关API
  async getNotifications(params = {}) {
    return this.get('/api/notifications', params);
  }

  async markNotificationRead(notificationId) {
    return this.put(`/api/notifications/${notificationId}/read`);
  }

  // 错误处理包装器
  async safeRequest(requestFn, defaultValue = null) {
    try {
      const result = await requestFn();
      return result;
    } catch (error) {
      console.error('HTTP请求错误:', error);
      return {
        success: false,
        error: error.message,
        data: defaultValue
      };
    }
  }

  // 批量请求
  async batchRequest(requests) {
    const promises = requests.map(async (req) => {
      try {
        const result = await this.request(req.url, req.options);
        return { ...result, id: req.id };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          id: req.id
        };
      }
    });

    return Promise.all(promises);
  }

  // 重试请求
  async retryRequest(requestFn, maxRetries = 3, delay = 1000) {
    let lastError;

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await requestFn();
      } catch (error) {
        lastError = error;
        
        if (i < maxRetries) {
          console.log(`请求失败，${delay}ms后进行第${i + 1}次重试`);
          await new Promise(resolve => setTimeout(resolve, delay));
          delay *= 2; // 指数退避
        }
      }
    }

    throw lastError;
  }
}

// 创建全局HTTP管理器实例
const httpManager = new HttpManager();

// 导出管理器
if (typeof module !== 'undefined' && module.exports) {
  module.exports = HttpManager;
} else if (typeof window !== 'undefined') {
  window.HttpManager = HttpManager;
  window.httpManager = httpManager;
}
