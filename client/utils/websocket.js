// WebSocket通信管理器
class WebSocketManager {
  constructor() {
    this.ws = null;
    this.url = '';
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 5000;
    this.messageHandlers = new Map();
    this.connectionStatus = 'disconnected';
    this.heartbeatInterval = null;
    this.heartbeatTimeout = null;
  }

  // 连接WebSocket
  connect(url) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('WebSocket已连接');
      return Promise.resolve();
    }

    this.url = url;
    this.connectionStatus = 'connecting';
    this.notifyStatusChange();

    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(url);

        this.ws.onopen = () => {
          console.log('WebSocket连接已建立');
          this.connectionStatus = 'connected';
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.notifyStatusChange();
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(event);
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket连接已关闭', event.code, event.reason);
          this.connectionStatus = 'disconnected';
          this.stopHeartbeat();
          this.notifyStatusChange();
          
          if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket错误:', error);
          this.connectionStatus = 'error';
          this.notifyStatusChange();
          reject(error);
        };

      } catch (error) {
        console.error('WebSocket连接失败:', error);
        this.connectionStatus = 'error';
        this.notifyStatusChange();
        reject(error);
      }
    });
  }

  // 断开连接
  disconnect() {
    if (this.ws) {
      this.ws.close(1000, '主动断开连接');
      this.ws = null;
    }
    this.stopHeartbeat();
    this.connectionStatus = 'disconnected';
    this.notifyStatusChange();
  }

  // 发送消息
  send(data) {
    return new Promise((resolve, reject) => {
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
        reject(new Error('WebSocket未连接'));
        return;
      }

      try {
        const message = typeof data === 'string' ? data : JSON.stringify(data);
        this.ws.send(message);
        resolve();
      } catch (error) {
        console.error('发送消息失败:', error);
        reject(error);
      }
    });
  }

  // 发送聊天消息
  sendChatMessage(message, userId) {
    return this.send({
      type: 'CHAT_MESSAGE',
      data: {
        message: message,
        userId: userId,
        timestamp: Date.now()
      }
    });
  }

  // 发送任务相关消息
  sendTaskMessage(type, taskData) {
    return this.send({
      type: 'TASK_MESSAGE',
      subType: type,
      data: taskData
    });
  }

  // 发送Cookie数据
  sendCookieData(domain, cookies) {
    return this.send({
      type: 'COOKIE_DATA',
      data: {
        domain: domain,
        cookies: cookies,
        timestamp: Date.now()
      }
    });
  }

  // 处理接收到的消息
  handleMessage(event) {
    try {
      const data = JSON.parse(event.data);
      console.log('收到WebSocket消息:', data);

      // 处理心跳响应
      if (data.type === 'PONG') {
        this.handlePong();
        return;
      }

      // 分发消息给注册的处理器
      const handlers = this.messageHandlers.get(data.type) || [];
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error('消息处理器错误:', error);
        }
      });

      // 通知background script
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.sendMessage({
          type: 'WEBSOCKET_MESSAGE',
          data: data
        });
      }

    } catch (error) {
      console.error('解析WebSocket消息失败:', error);
    }
  }

  // 注册消息处理器
  onMessage(type, handler) {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, []);
    }
    this.messageHandlers.get(type).push(handler);
  }

  // 移除消息处理器
  offMessage(type, handler) {
    const handlers = this.messageHandlers.get(type);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  // 获取连接状态
  getConnectionStatus() {
    return this.connectionStatus;
  }

  // 通知状态变化
  notifyStatusChange() {
    // 通知background script状态变化
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.sendMessage({
        type: 'WEBSOCKET_STATUS_CHANGE',
        status: this.connectionStatus
      });
    }

    // 触发自定义事件
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('websocketStatusChange', {
        detail: { status: this.connectionStatus }
      }));
    }
  }

  // 计划重连
  scheduleReconnect() {
    this.reconnectAttempts++;
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`${delay}ms后尝试第${this.reconnectAttempts}次重连`);
    
    setTimeout(() => {
      if (this.connectionStatus !== 'connected') {
        this.connect(this.url).catch(error => {
          console.error('重连失败:', error);
        });
      }
    }, delay);
  }

  // 开始心跳
  startHeartbeat() {
    this.stopHeartbeat();
    
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send({ type: 'PING', timestamp: Date.now() });
        
        // 设置心跳超时
        this.heartbeatTimeout = setTimeout(() => {
          console.log('心跳超时，关闭连接');
          this.ws.close();
        }, 10000);
      }
    }, 30000); // 每30秒发送一次心跳
  }

  // 停止心跳
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout);
      this.heartbeatTimeout = null;
    }
  }

  // 处理心跳响应
  handlePong() {
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout);
      this.heartbeatTimeout = null;
    }
  }

  // 检查连接是否活跃
  isConnected() {
    return this.ws && this.ws.readyState === WebSocket.OPEN;
  }

  // 等待连接建立
  waitForConnection(timeout = 10000) {
    return new Promise((resolve, reject) => {
      if (this.isConnected()) {
        resolve();
        return;
      }

      const timer = setTimeout(() => {
        reject(new Error('连接超时'));
      }, timeout);

      const checkConnection = () => {
        if (this.isConnected()) {
          clearTimeout(timer);
          resolve();
        } else if (this.connectionStatus === 'error') {
          clearTimeout(timer);
          reject(new Error('连接失败'));
        } else {
          setTimeout(checkConnection, 100);
        }
      };

      checkConnection();
    });
  }
}

// 创建全局WebSocket管理器实例
const wsManager = new WebSocketManager();

// 导出管理器
if (typeof module !== 'undefined' && module.exports) {
  module.exports = WebSocketManager;
} else if (typeof window !== 'undefined') {
  window.WebSocketManager = WebSocketManager;
  window.wsManager = wsManager;
}
