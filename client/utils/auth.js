// 认证管理器
class AuthManager {
  constructor() {
    this.cookieManager = null;
    this.loginCheckInterval = 30000; // 30秒检查一次
    this.loginCheckTimers = new Map();
    this.loginStatusCache = new Map();
    this.ssoUrls = new Map();
    this.loginCallbacks = new Map();
  }

  // 初始化认证管理器
  async init() {
    // 初始化Cookie管理器
    if (typeof window !== 'undefined' && window.cookieManager) {
      this.cookieManager = window.cookieManager;
    } else {
      const { CookieManager } = await import('./cookie-manager.js');
      this.cookieManager = new CookieManager();
    }

    // 加载SSO配置
    await this.loadSSOConfig();
  }

  // 加载SSO配置
  async loadSSOConfig() {
    try {
      const result = await chrome.storage.local.get(['settings']);
      const settings = result.settings || {};
      
      if (settings.ssoUrls) {
        Object.entries(settings.ssoUrls).forEach(([domain, url]) => {
          this.ssoUrls.set(domain, url);
        });
      }
    } catch (error) {
      console.error('加载SSO配置失败:', error);
    }
  }

  // 检查域名登录状态
  async checkLoginStatus(domain) {
    try {
      // 检查缓存
      const cached = this.loginStatusCache.get(domain);
      if (cached && (Date.now() - cached.timestamp) < 60000) { // 1分钟缓存
        return cached.result;
      }

      // 使用Cookie管理器检查登录状态
      const result = await this.cookieManager.checkLoginStatus(domain);
      
      // 更新缓存
      this.loginStatusCache.set(domain, {
        result: result,
        timestamp: Date.now()
      });

      console.log(`${domain} 登录状态:`, result.isLoggedIn);
      
      // 触发登录状态变化回调
      this.notifyLoginStatusChange(domain, result.isLoggedIn);
      
      return result;
    } catch (error) {
      console.error('检查登录状态失败:', error);
      return { success: false, isLoggedIn: false, error: error.message };
    }
  }

  // 开始监控域名登录状态
  startLoginMonitoring(domain) {
    // 如果已经在监控，先停止
    this.stopLoginMonitoring(domain);

    console.log(`开始监控 ${domain} 的登录状态`);

    // 立即检查一次
    this.checkLoginStatus(domain);

    // 设置定时检查
    const timer = setInterval(() => {
      this.checkLoginStatus(domain);
    }, this.loginCheckInterval);

    this.loginCheckTimers.set(domain, timer);

    // 监听Cookie变化
    const cookieListener = this.cookieManager.startCookieMonitoring(domain, (changeInfo) => {
      console.log(`${domain} Cookie变化:`, changeInfo);
      
      // 清除缓存并重新检查
      this.loginStatusCache.delete(domain);
      this.checkLoginStatus(domain);
    });

    // 保存监听器清理函数
    this.loginCheckTimers.set(`${domain}_cleanup`, cookieListener);
  }

  // 停止监控域名登录状态
  stopLoginMonitoring(domain) {
    const timer = this.loginCheckTimers.get(domain);
    if (timer) {
      clearInterval(timer);
      this.loginCheckTimers.delete(domain);
    }

    const cleanup = this.loginCheckTimers.get(`${domain}_cleanup`);
    if (cleanup) {
      cleanup();
      this.loginCheckTimers.delete(`${domain}_cleanup`);
    }

    console.log(`停止监控 ${domain} 的登录状态`);
  }

  // 注册登录状态变化回调
  onLoginStatusChange(domain, callback) {
    if (!this.loginCallbacks.has(domain)) {
      this.loginCallbacks.set(domain, []);
    }
    this.loginCallbacks.get(domain).push(callback);
  }

  // 移除登录状态变化回调
  offLoginStatusChange(domain, callback) {
    const callbacks = this.loginCallbacks.get(domain);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // 通知登录状态变化
  notifyLoginStatusChange(domain, isLoggedIn) {
    const callbacks = this.loginCallbacks.get(domain) || [];
    callbacks.forEach(callback => {
      try {
        callback(isLoggedIn, domain);
      } catch (error) {
        console.error('登录状态回调错误:', error);
      }
    });

    // 发送消息给其他组件
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.sendMessage({
        type: 'LOGIN_STATUS_CHANGE',
        domain: domain,
        isLoggedIn: isLoggedIn
      });
    }
  }

  // 跳转到SSO登录页面
  async redirectToSSO(domain) {
    try {
      const ssoUrl = this.ssoUrls.get(domain);
      if (!ssoUrl) {
        throw new Error(`未配置 ${domain} 的SSO登录地址`);
      }

      console.log(`跳转到SSO登录页面: ${ssoUrl}`);
      
      // 在新标签页中打开SSO登录页面
      await chrome.tabs.create({ url: ssoUrl });
      
      // 开始监控登录状态
      this.startLoginMonitoring(domain);
      
      return { success: true };
    } catch (error) {
      console.error('跳转SSO失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 设置SSO URL
  setSSOUrl(domain, url) {
    this.ssoUrls.set(domain, url);
    
    // 保存到存储
    this.saveSSOConfig();
  }

  // 获取SSO URL
  getSSOUrl(domain) {
    return this.ssoUrls.get(domain);
  }

  // 保存SSO配置
  async saveSSOConfig() {
    try {
      const result = await chrome.storage.local.get(['settings']);
      const settings = result.settings || {};
      
      settings.ssoUrls = Object.fromEntries(this.ssoUrls);
      
      await chrome.storage.local.set({ settings });
    } catch (error) {
      console.error('保存SSO配置失败:', error);
    }
  }

  // 自动提交Cookie到服务器
  async submitCookies(domain, serverUrl) {
    try {
      const result = await this.cookieManager.submitCookiesToServer(domain, serverUrl);
      
      if (result.success) {
        console.log(`${domain} Cookie提交成功`);
      } else {
        console.error(`${domain} Cookie提交失败:`, result.error);
      }
      
      return result;
    } catch (error) {
      console.error('提交Cookie失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 获取当前页面的登录状态
  async getCurrentPageLoginStatus() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs[0]) {
        const url = new URL(tabs[0].url);
        const domain = url.hostname;
        return await this.checkLoginStatus(domain);
      }
      
      return { success: false, error: '无法获取当前页面信息' };
    } catch (error) {
      console.error('获取当前页面登录状态失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 等待登录完成
  async waitForLogin(domain, timeout = 300000) { // 5分钟超时
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const checkLogin = async () => {
        try {
          const result = await this.checkLoginStatus(domain);
          
          if (result.success && result.isLoggedIn) {
            resolve(result);
            return;
          }
          
          if (Date.now() - startTime > timeout) {
            reject(new Error('等待登录超时'));
            return;
          }
          
          // 继续等待
          setTimeout(checkLogin, 2000); // 每2秒检查一次
        } catch (error) {
          reject(error);
        }
      };
      
      checkLogin();
    });
  }

  // 清除登录状态缓存
  clearLoginCache(domain = null) {
    if (domain) {
      this.loginStatusCache.delete(domain);
    } else {
      this.loginStatusCache.clear();
    }
  }

  // 获取所有监控的域名
  getMonitoredDomains() {
    return Array.from(this.loginCheckTimers.keys()).filter(key => !key.includes('_cleanup'));
  }

  // 停止所有监控
  stopAllMonitoring() {
    const domains = this.getMonitoredDomains();
    domains.forEach(domain => {
      this.stopLoginMonitoring(domain);
    });
  }

  // 获取登录统计信息
  async getLoginStats() {
    const domains = this.getMonitoredDomains();
    const stats = {
      totalDomains: domains.length,
      loggedInDomains: 0,
      loggedOutDomains: 0,
      unknownDomains: 0
    };

    for (const domain of domains) {
      try {
        const result = await this.checkLoginStatus(domain);
        if (result.success) {
          if (result.isLoggedIn) {
            stats.loggedInDomains++;
          } else {
            stats.loggedOutDomains++;
          }
        } else {
          stats.unknownDomains++;
        }
      } catch (error) {
        stats.unknownDomains++;
      }
    }

    return stats;
  }

  // 销毁认证管理器
  destroy() {
    this.stopAllMonitoring();
    this.loginStatusCache.clear();
    this.loginCallbacks.clear();
    this.ssoUrls.clear();
  }
}

// 创建全局认证管理器实例
const authManager = new AuthManager();

// 导出管理器
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AuthManager;
} else if (typeof window !== 'undefined') {
  window.AuthManager = AuthManager;
  window.authManager = authManager;
}
