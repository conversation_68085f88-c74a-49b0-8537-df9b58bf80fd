/* B端智能助手-APA 内容脚本样式 */

/* 重置样式，避免与目标网站样式冲突 */
#apa-floating-icon,
#apa-floating-icon *,
#apa-assistant-panel,
#apa-assistant-panel * {
  all: initial;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif !important;
}

/* 悬浮图标样式 */
#apa-floating-icon {
  position: fixed !important;
  top: 50% !important;
  right: 20px !important;
  transform: translateY(-50%) !important;
  z-index: 2147483647 !important; /* 最高层级 */
  cursor: pointer !important;
  user-select: none !important;
  transition: all 0.3s ease !important;
  width: 60px !important;
  height: 60px !important;
}

#apa-floating-icon:hover {
  transform: translateY(-50%) scale(1.05) !important;
}

.apa-icon-container {
  position: relative !important;
  width: 60px !important;
  height: 60px !important;
  border-radius: 50% !important;
  background: linear-gradient(135deg, #4A90E2, #357ABD) !important;
  box-shadow: 0 6px 24px rgba(74, 144, 226, 0.4) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease !important;
  border: 3px solid rgba(255, 255, 255, 0.2) !important;
}

.apa-icon-container:hover {
  box-shadow: 0 8px 32px rgba(74, 144, 226, 0.6) !important;
  transform: scale(1.02) !important;
}

.apa-robot-avatar {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: white !important;
}

.apa-robot-avatar svg {
  width: 32px !important;
  height: 32px !important;
  fill: currentColor !important;
}

/* 状态指示器 */
.apa-status-indicator {
  position: absolute !important;
  top: 2px !important;
  right: 2px !important;
  width: 14px !important;
  height: 14px !important;
  border-radius: 50% !important;
  border: 2px solid #fff !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.apa-status-connected {
  background-color: #4CAF50 !important;
}

.apa-status-connecting {
  background-color: #FF9800 !important;
  animation: apa-pulse 1.5s infinite !important;
}

.apa-status-disconnected {
  background-color: #F44336 !important;
}

.apa-status-error {
  background-color: #F44336 !important;
  animation: apa-blink 1s infinite !important;
}

/* 动画定义 */
@keyframes apa-pulse {
  0%, 100% { opacity: 1 !important; }
  50% { opacity: 0.5 !important; }
}

@keyframes apa-blink {
  0%, 50% { opacity: 1 !important; }
  51%, 100% { opacity: 0 !important; }
}

@keyframes apa-slideIn {
  from {
    opacity: 0 !important;
    transform: translateY(-50%) translateX(20px) !important;
  }
  to {
    opacity: 1 !important;
    transform: translateY(-50%) translateX(0) !important;
  }
}

@keyframes apa-slideOut {
  from {
    opacity: 1 !important;
    transform: translateY(-50%) translateX(0) !important;
  }
  to {
    opacity: 0 !important;
    transform: translateY(-50%) translateX(20px) !important;
  }
}

/* 助手面板样式 */
#apa-assistant-panel {
  position: fixed !important;
  top: 50% !important;
  right: 90px !important;
  transform: translateY(-50%) !important;
  width: 400px !important;
  height: 650px !important;
  background: #fff !important;
  border-radius: 16px !important;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.2) !important;
  z-index: 2147483646 !important;
  display: none !important;
  flex-direction: column !important;
  overflow: hidden !important;
  border: 1px solid #e0e0e0 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  color: #333 !important;
}

#apa-assistant-panel.visible {
  display: flex !important;
  animation: apa-slideIn 0.3s ease-out !important;
}

#apa-assistant-panel.hiding {
  animation: apa-slideOut 0.3s ease-in !important;
}

/* 面板头部 */
.apa-panel-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 20px !important;
  background: linear-gradient(135deg, #4A90E2, #357ABD) !important;
  color: white !important;
  border-radius: 16px 16px 0 0 !important;
}

.apa-header-left {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
}

.apa-title {
  font-weight: 600 !important;
  color: white !important;
  font-size: 18px !important;
  margin: 0 !important;
}

.login-status {
  font-size: 12px !important;
  color: rgba(255, 255, 255, 0.9) !important;
  padding: 4px 8px !important;
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 12px !important;
  cursor: pointer !important;
  transition: background 0.2s ease !important;
}

.login-status:hover {
  background: rgba(255, 255, 255, 0.3) !important;
}

.connection-status {
  font-size: 12px !important;
  padding: 4px 8px !important;
  border-radius: 12px !important;
  font-weight: 500 !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* 标签页样式 */
.apa-panel-tabs {
  display: flex !important;
  background: #fff !important;
  border-bottom: 1px solid #e0e0e0 !important;
}

.apa-tab {
  flex: 1 !important;
  padding: 16px !important;
  border: none !important;
  background: none !important;
  cursor: pointer !important;
  font-size: 14px !important;
  color: #666 !important;
  transition: all 0.2s ease !important;
  border-bottom: 3px solid transparent !important;
  font-weight: 500 !important;
  text-align: center !important;
}

.apa-tab:hover {
  background: #f8f9fa !important;
  color: #333 !important;
}

.apa-tab.active {
  color: #4A90E2 !important;
  border-bottom-color: #4A90E2 !important;
  background: #fff !important;
}

/* 内容区域 */
.apa-panel-content {
  flex: 1 !important;
  overflow: hidden !important;
  position: relative !important;
  background: #fff !important;
}

.apa-tab-content {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  padding: 20px !important;
  overflow-y: auto !important;
  display: none !important;
}

.apa-tab-content.active {
  display: block !important;
}

/* 加载状态 */
.loading {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
  color: #666 !important;
  font-size: 14px !important;
  flex-direction: column !important;
  gap: 12px !important;
}

.loading::before {
  content: '' !important;
  width: 24px !important;
  height: 24px !important;
  border: 2px solid #e0e0e0 !important;
  border-top: 2px solid #4A90E2 !important;
  border-radius: 50% !important;
  animation: apa-spin 1s linear infinite !important;
}

@keyframes apa-spin {
  0% { transform: rotate(0deg) !important; }
  100% { transform: rotate(360deg) !important; }
}

/* 滚动条样式 */
.apa-tab-content::-webkit-scrollbar {
  width: 6px !important;
}

.apa-tab-content::-webkit-scrollbar-track {
  background: #f1f1f1 !important;
  border-radius: 3px !important;
}

.apa-tab-content::-webkit-scrollbar-thumb {
  background: #c1c1c1 !important;
  border-radius: 3px !important;
}

.apa-tab-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8 !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  #apa-assistant-panel {
    width: 350px !important;
    height: 600px !important;
  }
}

@media (max-width: 768px) {
  #apa-floating-icon {
    right: 10px !important;
  }
  
  #apa-assistant-panel {
    width: 320px !important;
    height: 550px !important;
    right: 70px !important;
  }
}

/* 防止与目标网站样式冲突 */
#apa-floating-icon,
#apa-assistant-panel {
  box-sizing: border-box !important;
  text-align: left !important;
  direction: ltr !important;
}

#apa-floating-icon *,
#apa-assistant-panel * {
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  outline: none !important;
  text-decoration: none !important;
  list-style: none !important;
  background: transparent !important;
  color: inherit !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
  text-align: inherit !important;
  vertical-align: baseline !important;
}
