// 图标生成脚本
// 在浏览器控制台中运行此脚本来生成图标

function createIcon(size) {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d');
    
    // 清除画布
    ctx.clearRect(0, 0, size, size);
    
    // 计算中心点和半径
    const centerX = size / 2;
    const centerY = size / 2;
    const radius = size * 0.45;
    
    // 绘制背景圆形
    const gradient = ctx.createLinearGradient(0, 0, size, size);
    gradient.addColorStop(0, '#4A90E2');
    gradient.addColorStop(1, '#357ABD');
    
    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.fill();
    
    // 绘制边框
    if (size >= 32) {
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.lineWidth = Math.max(1, size * 0.02);
        ctx.stroke();
    }
    
    // 绘制机器人脸部特征
    ctx.fillStyle = 'white';
    
    // 眼睛大小和位置
    const eyeSize = Math.max(1, size * 0.08);
    const eyeY = centerY - size * 0.1;
    const eyeOffset = size * 0.15;
    
    // 左眼
    ctx.beginPath();
    ctx.arc(centerX - eyeOffset, eyeY, eyeSize, 0, 2 * Math.PI);
    ctx.fill();
    
    // 右眼
    ctx.beginPath();
    ctx.arc(centerX + eyeOffset, eyeY, eyeSize, 0, 2 * Math.PI);
    ctx.fill();
    
    // 嘴巴（只在较大尺寸显示）
    if (size >= 24) {
        ctx.strokeStyle = 'white';
        ctx.lineWidth = Math.max(1, size * 0.04);
        ctx.lineCap = 'round';
        ctx.beginPath();
        
        const mouthY = centerY + size * 0.08;
        const mouthWidth = size * 0.2;
        const mouthHeight = size * 0.08;
        
        // 绘制微笑弧线
        ctx.arc(centerX, mouthY - mouthHeight, mouthWidth, 0.3 * Math.PI, 0.7 * Math.PI);
        ctx.stroke();
    }
    
    // 天线（只在大尺寸显示）
    if (size >= 48) {
        ctx.strokeStyle = 'white';
        ctx.lineWidth = Math.max(1, size * 0.03);
        ctx.lineCap = 'round';
        
        const antennaHeight = size * 0.12;
        const antennaTop = centerY - radius - antennaHeight;
        
        // 天线杆
        ctx.beginPath();
        ctx.moveTo(centerX, centerY - radius);
        ctx.lineTo(centerX, antennaTop);
        ctx.stroke();
        
        // 天线顶部圆点
        ctx.fillStyle = 'white';
        ctx.beginPath();
        ctx.arc(centerX, antennaTop, size * 0.03, 0, 2 * Math.PI);
        ctx.fill();
    }
    
    return canvas;
}

function downloadIcon(canvas, size) {
    const link = document.createElement('a');
    link.download = `icon${size}.png`;
    link.href = canvas.toDataURL('image/png');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function generateAllIcons() {
    const sizes = [16, 32, 48, 128];
    
    console.log('开始生成图标...');
    
    sizes.forEach((size, index) => {
        setTimeout(() => {
            const canvas = createIcon(size);
            downloadIcon(canvas, size);
            console.log(`已生成 icon${size}.png`);
            
            if (index === sizes.length - 1) {
                console.log('所有图标生成完成！');
                console.log('请将下载的PNG文件放到 assets/icons/ 目录下');
            }
        }, index * 500); // 延迟下载避免浏览器阻止
    });
}

// 创建预览功能
function createIconPreview() {
    const sizes = [16, 32, 48, 128];
    const container = document.createElement('div');
    container.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        z-index: 10000;
        font-family: Arial, sans-serif;
    `;
    
    const title = document.createElement('h3');
    title.textContent = 'B端智能助手图标预览';
    title.style.margin = '0 0 15px 0';
    container.appendChild(title);
    
    const iconContainer = document.createElement('div');
    iconContainer.style.cssText = `
        display: flex;
        gap: 15px;
        align-items: center;
        margin-bottom: 15px;
    `;
    
    sizes.forEach(size => {
        const iconWrapper = document.createElement('div');
        iconWrapper.style.textAlign = 'center';
        
        const canvas = createIcon(size);
        canvas.style.cssText = `
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
        `;
        
        const label = document.createElement('div');
        label.textContent = `${size}x${size}`;
        label.style.cssText = `
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        `;
        
        iconWrapper.appendChild(canvas);
        iconWrapper.appendChild(label);
        iconContainer.appendChild(iconWrapper);
    });
    
    container.appendChild(iconContainer);
    
    const downloadBtn = document.createElement('button');
    downloadBtn.textContent = '下载所有图标';
    downloadBtn.style.cssText = `
        background: #4A90E2;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        margin-right: 10px;
    `;
    downloadBtn.onclick = generateAllIcons;
    
    const closeBtn = document.createElement('button');
    closeBtn.textContent = '关闭';
    closeBtn.style.cssText = `
        background: #666;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
    `;
    closeBtn.onclick = () => document.body.removeChild(container);
    
    container.appendChild(downloadBtn);
    container.appendChild(closeBtn);
    document.body.appendChild(container);
}

// 导出函数供外部使用
if (typeof window !== 'undefined') {
    window.generateAllIcons = generateAllIcons;
    window.createIconPreview = createIconPreview;
    window.createIcon = createIcon;
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        generateAllIcons,
        createIconPreview,
        createIcon
    };
}

// 使用说明
console.log(`
图标生成脚本已加载！

使用方法：
1. 在浏览器控制台运行: createIconPreview()
2. 或直接运行: generateAllIcons()

生成的图标文件需要放到 assets/icons/ 目录下：
- icon16.png
- icon32.png  
- icon48.png
- icon128.png

然后在 manifest.json 中恢复图标配置。
`);
