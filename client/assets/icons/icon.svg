<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义渐变 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357ABD;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景圆形 -->
  <circle cx="64" cy="64" r="58" fill="url(#bgGradient)" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>

  <!-- 眼睛 -->
  <circle cx="52" cy="56" r="8" fill="#fff"/>
  <circle cx="76" cy="56" r="8" fill="#fff"/>
  <circle cx="52" cy="56" r="4" fill="#333"/>
  <circle cx="76" cy="56" r="4" fill="#333"/>

  <!-- 嘴巴 -->
  <path d="M48 76 Q64 88 80 76" stroke="#fff" stroke-width="4" fill="none" stroke-linecap="round"/>

  <!-- 天线 -->
  <line x1="64" y1="6" x2="64" y2="20" stroke="#fff" stroke-width="3" stroke-linecap="round"/>
  <circle cx="64" cy="6" r="3" fill="#fff"/>
</svg>
