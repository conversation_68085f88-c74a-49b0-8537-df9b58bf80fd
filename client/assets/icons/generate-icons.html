<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成插件图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .icon-item {
            text-align: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #fafafa;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 5px 0;
        }
        button {
            background: #4A90E2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #357ABD;
        }
        .download-all {
            background: #4CAF50;
            font-size: 16px;
            padding: 15px 30px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>B端智能助手-APA 图标生成器</h1>
        <p>这个工具会生成Chrome插件所需的各种尺寸图标文件。</p>

        <div class="icon-preview" id="iconPreview">
            <!-- 图标预览将在这里生成 -->
        </div>

        <button class="download-all" onclick="downloadAllIcons()">下载所有图标</button>

        <div style="margin-top: 30px;">
            <h3>使用说明：</h3>
            <ol>
                <li>点击"下载所有图标"按钮</li>
                <li>将下载的PNG文件放到 assets/icons/ 目录下</li>
                <li>重新加载Chrome插件</li>
            </ol>
        </div>
    </div>

    <script>
        // 图标尺寸配置
        const iconSizes = [16, 32, 48, 128];

        // 创建图标
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');

            // 清除画布并设置高质量渲染
            ctx.clearRect(0, 0, size, size);
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = 'high';

            // 计算中心点和半径
            const centerX = size / 2;
            const centerY = size / 2;
            const radius = size * 0.45;

            // 绘制背景圆形（渐变）
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#4A90E2');
            gradient.addColorStop(1, '#357ABD');

            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fill();

            // 绘制边框
            if (size >= 24) {
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
                ctx.lineWidth = Math.max(1, size * 0.015);
                ctx.stroke();
            }

            // 绘制机器人脸部特征
            ctx.fillStyle = 'white';

            // 眼睛
            const eyeSize = Math.max(2, size * 0.08);
            const eyeY = centerY - size * 0.1;
            const eyeOffset = size * 0.15;

            // 左眼
            ctx.beginPath();
            ctx.arc(centerX - eyeOffset, eyeY, eyeSize, 0, 2 * Math.PI);
            ctx.fill();

            // 右眼
            ctx.beginPath();
            ctx.arc(centerX + eyeOffset, eyeY, eyeSize, 0, 2 * Math.PI);
            ctx.fill();

            // 眼珠（只在较大尺寸显示）
            if (size >= 32) {
                ctx.fillStyle = '#333';
                const pupilSize = eyeSize * 0.5;

                ctx.beginPath();
                ctx.arc(centerX - eyeOffset, eyeY, pupilSize, 0, 2 * Math.PI);
                ctx.fill();

                ctx.beginPath();
                ctx.arc(centerX + eyeOffset, eyeY, pupilSize, 0, 2 * Math.PI);
                ctx.fill();
            }

            // 嘴巴
            if (size >= 20) {
                ctx.strokeStyle = 'white';
                ctx.lineWidth = Math.max(1, size * 0.04);
                ctx.lineCap = 'round';
                ctx.beginPath();

                const mouthY = centerY + size * 0.08;
                const mouthRadius = size * 0.15;

                // 绘制微笑弧线
                ctx.arc(centerX, mouthY - mouthRadius * 0.5, mouthRadius, 0.3 * Math.PI, 0.7 * Math.PI);
                ctx.stroke();
            }

            // 天线（只在大尺寸显示）
            if (size >= 48) {
                ctx.strokeStyle = 'white';
                ctx.lineWidth = Math.max(1, size * 0.025);
                ctx.lineCap = 'round';

                const antennaHeight = size * 0.1;
                const antennaTop = centerY - radius - antennaHeight;

                // 天线杆
                ctx.beginPath();
                ctx.moveTo(centerX, centerY - radius + size * 0.02);
                ctx.lineTo(centerX, antennaTop);
                ctx.stroke();

                // 天线顶部圆点
                ctx.fillStyle = 'white';
                ctx.beginPath();
                ctx.arc(centerX, antennaTop, size * 0.025, 0, 2 * Math.PI);
                ctx.fill();
            }

            return canvas;
        }

        // 生成所有尺寸的图标预览
        function generatePreviews() {
            const container = document.getElementById('iconPreview');
            container.innerHTML = '';

            iconSizes.forEach(size => {
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';

                const canvas = createIcon(size);
                const title = document.createElement('h4');
                title.textContent = `${size}x${size}`;

                const downloadBtn = document.createElement('button');
                downloadBtn.textContent = '下载';
                downloadBtn.onclick = () => downloadIcon(canvas, size);

                iconItem.appendChild(title);
                iconItem.appendChild(canvas);
                iconItem.appendChild(downloadBtn);
                container.appendChild(iconItem);
            });
        }

        // 下载单个图标
        function downloadIcon(canvas, size) {
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        // 下载所有图标
        function downloadAllIcons() {
            iconSizes.forEach(size => {
                setTimeout(() => {
                    const canvas = createIcon(size);
                    downloadIcon(canvas, size);
                }, size * 10); // 延迟下载避免浏览器阻止
            });
        }

        // 页面加载时生成预览
        window.onload = generatePreviews;
    </script>
</body>
</html>
