<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>B端智能助手-APA</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="popup-container">
    <div class="popup-header">
      <div class="header-left">
        <img src="../assets/icons/icon32.png" alt="APA" class="logo">
        <h1 class="title">智能助手</h1>
      </div>
      <div class="header-right">
        <span id="connection-status" class="connection-status disconnected">未连接</span>
      </div>
    </div>

    <div class="popup-tabs">
      <button class="tab-button active" data-tab="overview">概览</button>
      <button class="tab-button" data-tab="tasks">任务</button>
      <button class="tab-button" data-tab="settings">设置</button>
    </div>

    <div class="popup-content">
      <!-- 概览标签页 -->
      <div id="overview-tab" class="tab-content active">
        <div class="overview-section">
          <div class="status-card">
            <div class="status-item">
              <span class="status-label">当前站点:</span>
              <span id="current-site" class="status-value">检测中...</span>
            </div>
            <div class="status-item">
              <span class="status-label">登录状态:</span>
              <span id="login-status" class="status-value">检查中...</span>
            </div>
            <div class="status-item">
              <span class="status-label">服务连接:</span>
              <span id="server-status" class="status-value">检查中...</span>
            </div>
          </div>

          <div class="quick-actions">
            <h3>快速操作</h3>
            <div class="action-buttons">
              <button id="open-assistant" class="action-btn primary">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                  <path d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 14c-3.3 0-6-2.7-6-6s2.7-6 6-6 6 2.7 6 6-2.7 6-6 6z"/>
                  <circle cx="6" cy="6" r="1"/>
                  <circle cx="10" cy="6" r="1"/>
                  <path d="M5.5 10.5c.8.8 2.2.8 3 0" stroke="currentColor" fill="none" stroke-linecap="round"/>
                </svg>
                打开助手面板
              </button>
              <button id="new-task" class="action-btn">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                  <path d="M8 0v16M0 8h16" stroke="currentColor" stroke-width="2"/>
                </svg>
                新建任务
              </button>
              <button id="view-tasks" class="action-btn">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                  <path d="M2 3h12v2H2V3zm0 4h12v2H2V7zm0 4h12v2H2v-2z"/>
                </svg>
                查看任务
              </button>
            </div>
          </div>

          <div class="recent-activity">
            <h3>最近活动</h3>
            <div id="activity-list" class="activity-list">
              <div class="activity-item">
                <div class="activity-icon">📋</div>
                <div class="activity-content">
                  <div class="activity-title">暂无活动记录</div>
                  <div class="activity-time">请开始使用助手功能</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 任务标签页 -->
      <div id="tasks-tab" class="tab-content">
        <div class="tasks-section">
          <div class="section-header">
            <h3>任务管理</h3>
            <button id="refresh-tasks" class="refresh-btn">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M8 0v4l3-3 3 3V0H8zM0 8h4l-3 3 3 3H0V8z"/>
              </svg>
            </button>
          </div>

          <div class="task-filters">
            <select id="task-status-filter" class="filter-select">
              <option value="">所有状态</option>
              <option value="pending">待执行</option>
              <option value="running">执行中</option>
              <option value="completed">已完成</option>
              <option value="failed">失败</option>
            </select>
            <input type="date" id="task-date-filter" class="filter-input">
          </div>

          <div id="task-list" class="task-list">
            <div class="empty-state">
              <div class="empty-icon">📝</div>
              <div class="empty-text">暂无任务</div>
              <button id="create-first-task" class="create-task-btn">创建第一个任务</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 设置标签页 -->
      <div id="settings-tab" class="tab-content">
        <div class="settings-section">
          <form id="settings-form">
            <div class="setting-group">
              <label for="server-address">服务器地址</label>
              <input type="text" id="server-address" placeholder="ws://localhost:8080" class="setting-input">
              <small class="setting-help">WebSocket服务器地址</small>
            </div>

            <div class="setting-group">
              <label for="target-sites">代理站点</label>
              <select id="target-sites" class="setting-select" multiple>
                <option value="">从服务器加载...</option>
              </select>
              <small class="setting-help">选择要代理的目标站点</small>
            </div>

            <div class="setting-group">
              <label>通知设置</label>
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input type="checkbox" id="desktop-notifications" checked>
                  <span class="checkmark"></span>
                  桌面通知
                </label>
                <label class="checkbox-label">
                  <input type="checkbox" id="sound-alerts" checked>
                  <span class="checkmark"></span>
                  声音提醒
                </label>
              </div>
            </div>

            <div class="setting-group">
              <label for="auto-login">自动登录检测</label>
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input type="checkbox" id="auto-login" checked>
                  <span class="checkmark"></span>
                  自动检测登录状态
                </label>
              </div>
              <small class="setting-help">自动检测目标站点的登录状态</small>
            </div>

            <div class="setting-actions">
              <button type="button" id="test-connection" class="test-btn">测试连接</button>
              <button type="submit" class="save-btn">保存设置</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="popup-footer">
      <div class="footer-info">
        <span class="version">v1.0.0</span>
        <span class="separator">|</span>
        <a href="#" id="help-link">帮助</a>
      </div>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
