// B端智能助手-APA Popup脚本
class PopupManager {
  constructor() {
    this.currentTab = 'overview';
    this.settings = {};
    this.connectionStatus = 'disconnected';
    this.init();
  }

  async init() {
    // 初始化事件监听
    this.initEventListeners();
    
    // 加载设置
    await this.loadSettings();
    
    // 更新UI状态
    this.updateConnectionStatus();
    this.updateCurrentSite();
    this.updateLoginStatus();
    
    // 加载任务列表
    this.loadTasks();
  }

  initEventListeners() {
    // 标签页切换
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        const tabName = button.dataset.tab;
        this.switchTab(tabName);
      });
    });

    // 快速操作按钮
    document.getElementById('open-assistant')?.addEventListener('click', () => {
      this.openAssistantPanel();
    });

    document.getElementById('new-task')?.addEventListener('click', () => {
      this.createNewTask();
    });

    document.getElementById('view-tasks')?.addEventListener('click', () => {
      this.switchTab('tasks');
    });

    document.getElementById('create-first-task')?.addEventListener('click', () => {
      this.createNewTask();
    });

    // 任务管理
    document.getElementById('refresh-tasks')?.addEventListener('click', () => {
      this.loadTasks();
    });

    document.getElementById('task-status-filter')?.addEventListener('change', () => {
      this.filterTasks();
    });

    document.getElementById('task-date-filter')?.addEventListener('change', () => {
      this.filterTasks();
    });

    // 设置表单
    document.getElementById('settings-form')?.addEventListener('submit', (e) => {
      e.preventDefault();
      this.saveSettings();
    });

    document.getElementById('test-connection')?.addEventListener('click', () => {
      this.testConnection();
    });

    // 帮助链接
    document.getElementById('help-link')?.addEventListener('click', (e) => {
      e.preventDefault();
      this.openHelp();
    });
  }

  switchTab(tabName) {
    // 更新按钮状态
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
      button.classList.toggle('active', button.dataset.tab === tabName);
    });

    // 更新内容区域
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
      content.classList.toggle('active', content.id === `${tabName}-tab`);
    });

    this.currentTab = tabName;

    // 根据标签页加载相应内容
    switch (tabName) {
      case 'overview':
        this.loadOverview();
        break;
      case 'tasks':
        this.loadTasks();
        break;
      case 'settings':
        this.loadSettingsForm();
        break;
    }
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.local.get(['settings']);
      this.settings = result.settings || {
        serverAddress: 'ws://localhost:8080',
        targetSites: [],
        ssoUrls: {},
        desktopNotifications: true,
        soundAlerts: true,
        autoLogin: true
      };
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  }

  async saveSettings() {
    try {
      // 从表单获取设置值
      const formData = new FormData(document.getElementById('settings-form'));
      const newSettings = {
        serverAddress: document.getElementById('server-address').value,
        targetSites: Array.from(document.getElementById('target-sites').selectedOptions).map(opt => opt.value),
        desktopNotifications: document.getElementById('desktop-notifications').checked,
        soundAlerts: document.getElementById('sound-alerts').checked,
        autoLogin: document.getElementById('auto-login').checked
      };

      // 合并设置
      this.settings = { ...this.settings, ...newSettings };

      // 保存到存储
      await chrome.storage.local.set({ settings: this.settings });

      // 通知background script更新设置
      chrome.runtime.sendMessage({
        type: 'UPDATE_SETTINGS',
        settings: this.settings
      });

      this.showMessage('设置已保存', 'success');
    } catch (error) {
      console.error('保存设置失败:', error);
      this.showMessage('保存设置失败', 'error');
    }
  }

  loadSettingsForm() {
    // 填充设置表单
    document.getElementById('server-address').value = this.settings.serverAddress || '';
    document.getElementById('desktop-notifications').checked = this.settings.desktopNotifications !== false;
    document.getElementById('sound-alerts').checked = this.settings.soundAlerts !== false;
    document.getElementById('auto-login').checked = this.settings.autoLogin !== false;

    // 加载目标站点选项
    this.loadTargetSites();
  }

  async loadTargetSites() {
    // 这里应该从服务器获取可用的目标站点
    // 暂时使用模拟数据
    const sites = [
      { value: 'erp.example.com', text: '积理ERP' },
      { value: 'wanshang.example.com', text: '万商系统' },
      { value: 'haibo.example.com', text: '海博系统' }
    ];

    const select = document.getElementById('target-sites');
    select.innerHTML = '';
    
    sites.forEach(site => {
      const option = document.createElement('option');
      option.value = site.value;
      option.textContent = site.text;
      option.selected = this.settings.targetSites?.includes(site.value);
      select.appendChild(option);
    });
  }

  async testConnection() {
    const button = document.getElementById('test-connection');
    const originalText = button.textContent;
    
    button.textContent = '测试中...';
    button.disabled = true;

    try {
      // 发送测试连接请求
      const response = await chrome.runtime.sendMessage({
        type: 'TEST_CONNECTION',
        serverAddress: document.getElementById('server-address').value
      });

      if (response && response.success) {
        this.showMessage('连接测试成功', 'success');
      } else {
        this.showMessage('连接测试失败', 'error');
      }
    } catch (error) {
      console.error('测试连接失败:', error);
      this.showMessage('连接测试失败', 'error');
    } finally {
      button.textContent = originalText;
      button.disabled = false;
    }
  }

  updateConnectionStatus() {
    chrome.runtime.sendMessage({ type: 'GET_CONNECTION_STATUS' }, (response) => {
      if (response) {
        this.connectionStatus = response.status;
        const statusElement = document.getElementById('connection-status');
        const serverStatusElement = document.getElementById('server-status');
        
        if (statusElement) {
          statusElement.textContent = this.getStatusText(response.status);
          statusElement.className = `connection-status ${response.status}`;
        }
        
        if (serverStatusElement) {
          serverStatusElement.textContent = this.getStatusText(response.status);
        }
      }
    });
  }

  getStatusText(status) {
    switch (status) {
      case 'connected': return '已连接';
      case 'connecting': return '连接中';
      case 'disconnected': return '未连接';
      case 'error': return '连接错误';
      default: return '未知状态';
    }
  }

  async updateCurrentSite() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs[0]) {
        const url = new URL(tabs[0].url);
        const domain = url.hostname;
        
        const currentSiteElement = document.getElementById('current-site');
        if (currentSiteElement) {
          currentSiteElement.textContent = domain;
        }
      }
    } catch (error) {
      console.error('获取当前站点失败:', error);
    }
  }

  async updateLoginStatus() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs[0]) {
        const url = new URL(tabs[0].url);
        const domain = url.hostname;
        
        chrome.runtime.sendMessage({
          type: 'CHECK_LOGIN_STATUS',
          domain: domain
        }, (response) => {
          const loginStatusElement = document.getElementById('login-status');
          if (loginStatusElement && response) {
            loginStatusElement.textContent = response.isLoggedIn ? '已登录' : '未登录';
            loginStatusElement.style.color = response.isLoggedIn ? '#4CAF50' : '#F44336';
          }
        });
      }
    } catch (error) {
      console.error('检查登录状态失败:', error);
    }
  }

  loadOverview() {
    // 加载最近活动
    this.loadRecentActivity();
  }

  loadRecentActivity() {
    // 这里应该从存储或服务器加载最近活动
    // 暂时显示占位内容
    const activityList = document.getElementById('activity-list');
    if (activityList) {
      // 如果没有活动记录，显示默认内容
      activityList.innerHTML = `
        <div class="activity-item">
          <div class="activity-icon">📋</div>
          <div class="activity-content">
            <div class="activity-title">暂无活动记录</div>
            <div class="activity-time">请开始使用助手功能</div>
          </div>
        </div>
      `;
    }
  }

  async loadTasks() {
    const taskList = document.getElementById('task-list');
    if (!taskList) return;

    // 显示加载状态
    taskList.innerHTML = '<div class="loading">加载任务列表...</div>';

    try {
      // 这里应该从服务器获取任务列表
      // 暂时使用模拟数据
      const tasks = [];

      if (tasks.length === 0) {
        taskList.innerHTML = `
          <div class="empty-state">
            <div class="empty-icon">📝</div>
            <div class="empty-text">暂无任务</div>
            <button id="create-first-task" class="create-task-btn">创建第一个任务</button>
          </div>
        `;
        
        // 重新绑定事件
        document.getElementById('create-first-task')?.addEventListener('click', () => {
          this.createNewTask();
        });
      } else {
        this.renderTaskList(tasks);
      }
    } catch (error) {
      console.error('加载任务失败:', error);
      taskList.innerHTML = '<div class="error">加载任务失败</div>';
    }
  }

  renderTaskList(tasks) {
    const taskList = document.getElementById('task-list');
    if (!taskList) return;

    taskList.innerHTML = tasks.map(task => `
      <div class="task-item" data-task-id="${task.id}">
        <div class="task-header">
          <span class="task-name">${task.name}</span>
          <span class="task-status ${task.status}">${this.getTaskStatusText(task.status)}</span>
        </div>
        <div class="task-meta">
          <span class="task-id">任务编号: ${task.id}</span>
          <span class="task-time">${task.createTime}</span>
        </div>
        <div class="task-actions">
          <button class="task-action-btn" onclick="viewTaskDetail('${task.id}')">查看详情</button>
        </div>
      </div>
    `).join('');
  }

  getTaskStatusText(status) {
    switch (status) {
      case 'pending': return '待执行';
      case 'running': return '执行中';
      case 'completed': return '已完成';
      case 'failed': return '失败';
      default: return '未知';
    }
  }

  filterTasks() {
    // 实现任务过滤逻辑
    const statusFilter = document.getElementById('task-status-filter').value;
    const dateFilter = document.getElementById('task-date-filter').value;
    
    // 重新加载任务列表（带过滤条件）
    this.loadTasks();
  }

  async openAssistantPanel() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, {
          type: 'TOGGLE_ASSISTANT_PANEL'
        });
        
        // 关闭popup
        window.close();
      }
    } catch (error) {
      console.error('打开助手面板失败:', error);
    }
  }

  createNewTask() {
    // 这里应该打开新任务创建界面
    console.log('创建新任务');
    this.showMessage('新任务创建功能开发中', 'info');
  }

  openHelp() {
    // 打开帮助页面
    chrome.tabs.create({
      url: 'https://example.com/help'
    });
  }

  showMessage(text, type = 'info') {
    // 创建消息提示
    const message = document.createElement('div');
    message.className = `message message-${type}`;
    message.textContent = text;
    message.style.cssText = `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      padding: 12px 20px;
      border-radius: 6px;
      color: white;
      font-weight: 500;
      z-index: 10000;
      animation: slideDown 0.3s ease-out;
    `;

    switch (type) {
      case 'success':
        message.style.background = '#4CAF50';
        break;
      case 'error':
        message.style.background = '#F44336';
        break;
      case 'warning':
        message.style.background = '#FF9800';
        break;
      default:
        message.style.background = '#2196F3';
    }

    document.body.appendChild(message);

    // 3秒后自动移除
    setTimeout(() => {
      message.remove();
    }, 3000);
  }
}

// 全局函数
window.viewTaskDetail = function(taskId) {
  console.log('查看任务详情:', taskId);
  // 实现任务详情查看逻辑
};

// 初始化popup管理器
document.addEventListener('DOMContentLoaded', () => {
  new PopupManager();
});

// 添加动画样式
const style = document.createElement('style');
style.textContent = `
  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  .loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #666;
    font-size: 14px;
  }

  .error {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #F44336;
    font-size: 14px;
  }

  .task-item {
    padding: 16px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    margin-bottom: 12px;
    background: #fff;
    transition: box-shadow 0.2s ease;
  }

  .task-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .task-name {
    font-weight: 500;
    color: #333;
  }

  .task-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
  }

  .task-status.pending {
    background: #FFF3E0;
    color: #F57C00;
  }

  .task-status.running {
    background: #E3F2FD;
    color: #1976D2;
  }

  .task-status.completed {
    background: #E8F5E8;
    color: #388E3C;
  }

  .task-status.failed {
    background: #FFEBEE;
    color: #D32F2F;
  }

  .task-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
    margin-bottom: 12px;
  }

  .task-actions {
    display: flex;
    gap: 8px;
  }

  .task-action-btn {
    padding: 6px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background: #fff;
    color: #666;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
  }

  .task-action-btn:hover {
    background: #f8f9fa;
    border-color: #4A90E2;
    color: #4A90E2;
  }
`;
document.head.appendChild(style);
