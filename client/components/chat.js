// 智能对话组件
class ChatComponent {
  constructor(container) {
    this.container = container;
    this.messages = [];
    this.isTyping = false;
    this.sessionId = this.generateSessionId();
    this.quickCommands = [
      { text: '查看任务状态', command: '/status' },
      { text: '帮助文档', command: '/help' },
      { text: '系统设置', command: '/settings' },
      { text: '清空对话', command: '/clear' }
    ];
    this.init();
  }

  async init() {
    this.render();
    this.bindEvents();
    this.addWelcomeMessage();
    await this.loadChatHistory();
  }

  render() {
    this.container.innerHTML = `
      <div class="chat-component">
        <div class="chat-header">
          <div class="chat-title">
            <span class="bot-avatar">🤖</span>
            <div class="title-info">
              <h3>智能助手</h3>
              <span class="status online">在线</span>
            </div>
          </div>
          <div class="chat-actions">
            <button class="action-btn" id="clear-chat" title="清空对话">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M2 3h12l-1 10H3L2 3zm2 0V1h8v2"/>
              </svg>
            </button>
          </div>
        </div>

        <div class="chat-messages" id="chat-messages">
          ${this.renderMessages()}
        </div>

        <div class="quick-commands" id="quick-commands">
          ${this.renderQuickCommands()}
        </div>

        <div class="chat-input-area">
          <div class="input-container">
            <textarea 
              id="chat-input" 
              placeholder="输入消息..." 
              rows="1"
              maxlength="1000"
            ></textarea>
            <button class="send-btn" id="send-btn" disabled>
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M15 8L1 1v6l10 1-10 1v6l14-7z"/>
              </svg>
            </button>
          </div>
          <div class="input-footer">
            <span class="char-count">0/1000</span>
            <span class="input-hint">按 Enter 发送，Shift+Enter 换行</span>
          </div>
        </div>
      </div>
    `;

    this.addStyles();
  }

  renderMessages() {
    if (this.messages.length === 0) {
      return '<div class="empty-chat">开始对话吧...</div>';
    }

    return this.messages.map(message => this.renderMessage(message)).join('');
  }

  renderMessage(message) {
    const isUser = message.sender === 'user';
    const timeStr = this.formatTime(message.timestamp);
    
    return `
      <div class="message ${isUser ? 'user-message' : 'bot-message'}">
        <div class="message-avatar">
          ${isUser ? '👤' : '🤖'}
        </div>
        <div class="message-content">
          <div class="message-bubble">
            ${this.formatMessageContent(message.content)}
          </div>
          <div class="message-time">${timeStr}</div>
        </div>
      </div>
    `;
  }

  renderQuickCommands() {
    return this.quickCommands.map(cmd => `
      <button class="quick-cmd-btn" data-command="${cmd.command}">
        ${cmd.text}
      </button>
    `).join('');
  }

  bindEvents() {
    const chatInput = this.container.querySelector('#chat-input');
    const sendBtn = this.container.querySelector('#send-btn');
    const messagesContainer = this.container.querySelector('#chat-messages');

    // 输入框事件
    chatInput.addEventListener('input', (e) => {
      this.handleInputChange(e);
    });

    chatInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.sendMessage();
      }
    });

    // 发送按钮
    sendBtn.addEventListener('click', () => {
      this.sendMessage();
    });

    // 快捷命令
    this.container.addEventListener('click', (e) => {
      if (e.target.classList.contains('quick-cmd-btn')) {
        const command = e.target.dataset.command;
        this.executeQuickCommand(command);
      }
    });

    // 清空对话
    this.container.addEventListener('click', (e) => {
      if (e.target.closest('#clear-chat')) {
        this.clearChat();
      }
    });

    // 自动调整输入框高度
    chatInput.addEventListener('input', () => {
      this.autoResizeTextarea(chatInput);
    });
  }

  handleInputChange(e) {
    const input = e.target;
    const sendBtn = this.container.querySelector('#send-btn');
    const charCount = this.container.querySelector('.char-count');
    
    const length = input.value.length;
    charCount.textContent = `${length}/1000`;
    
    sendBtn.disabled = length === 0 || this.isTyping;
    
    if (length > 950) {
      charCount.style.color = '#F44336';
    } else {
      charCount.style.color = '#666';
    }
  }

  async sendMessage() {
    const chatInput = this.container.querySelector('#chat-input');
    const message = chatInput.value.trim();
    
    if (!message || this.isTyping) return;

    // 添加用户消息
    this.addMessage({
      sender: 'user',
      content: message,
      timestamp: Date.now()
    });

    // 清空输入框
    chatInput.value = '';
    this.handleInputChange({ target: chatInput });
    this.autoResizeTextarea(chatInput);

    // 显示机器人正在输入
    this.showTypingIndicator();

    try {
      // 发送到服务器
      const response = await this.sendToServer(message);
      
      // 隐藏输入指示器
      this.hideTypingIndicator();
      
      // 添加机器人回复
      this.addMessage({
        sender: 'bot',
        content: response.message || '抱歉，我现在无法回答这个问题。',
        timestamp: Date.now()
      });

    } catch (error) {
      console.error('发送消息失败:', error);
      this.hideTypingIndicator();
      
      this.addMessage({
        sender: 'bot',
        content: '抱歉，发送消息时出现了错误，请稍后重试。',
        timestamp: Date.now()
      });
    }
  }

  addMessage(message) {
    this.messages.push(message);
    this.updateMessagesDisplay();
    this.scrollToBottom();
    this.saveChatHistory();
  }

  updateMessagesDisplay() {
    const messagesContainer = this.container.querySelector('#chat-messages');
    messagesContainer.innerHTML = this.renderMessages();
  }

  showTypingIndicator() {
    this.isTyping = true;
    const messagesContainer = this.container.querySelector('#chat-messages');
    
    const typingIndicator = document.createElement('div');
    typingIndicator.id = 'typing-indicator';
    typingIndicator.className = 'message bot-message typing';
    typingIndicator.innerHTML = `
      <div class="message-avatar">🤖</div>
      <div class="message-content">
        <div class="message-bubble">
          <div class="typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    `;
    
    messagesContainer.appendChild(typingIndicator);
    this.scrollToBottom();
  }

  hideTypingIndicator() {
    this.isTyping = false;
    const typingIndicator = this.container.querySelector('#typing-indicator');
    if (typingIndicator) {
      typingIndicator.remove();
    }
    
    const sendBtn = this.container.querySelector('#send-btn');
    const chatInput = this.container.querySelector('#chat-input');
    sendBtn.disabled = chatInput.value.trim().length === 0;
  }

  async sendToServer(message) {
    // 模拟发送到服务器
    return new Promise((resolve) => {
      setTimeout(() => {
        // 简单的回复逻辑
        let reply = '我收到了您的消息，正在处理中...';
        
        if (message.includes('任务')) {
          reply = '您可以在任务管理页面查看和创建任务。需要我帮您打开任务页面吗？';
        } else if (message.includes('帮助')) {
          reply = '我可以帮您：\n1. 管理和监控任务\n2. 查看系统状态\n3. 配置设置\n4. 解答使用问题\n\n请告诉我您需要什么帮助？';
        } else if (message.includes('设置')) {
          reply = '您可以在设置页面配置服务器地址、代理站点等选项。需要我为您介绍具体设置项吗？';
        }
        
        resolve({ message: reply });
      }, 1000 + Math.random() * 2000);
    });
  }

  executeQuickCommand(command) {
    const chatInput = this.container.querySelector('#chat-input');
    
    switch (command) {
      case '/clear':
        this.clearChat();
        break;
      case '/help':
        chatInput.value = '请告诉我使用帮助';
        this.sendMessage();
        break;
      case '/status':
        chatInput.value = '查看当前任务状态';
        this.sendMessage();
        break;
      case '/settings':
        chatInput.value = '如何配置系统设置？';
        this.sendMessage();
        break;
      default:
        chatInput.value = command;
        chatInput.focus();
    }
  }

  clearChat() {
    if (confirm('确定要清空所有对话记录吗？')) {
      this.messages = [];
      this.updateMessagesDisplay();
      this.addWelcomeMessage();
      this.clearChatHistory();
    }
  }

  addWelcomeMessage() {
    this.addMessage({
      sender: 'bot',
      content: '您好！我是您的智能助手，可以帮您管理任务、查看状态和解答问题。有什么可以帮您的吗？',
      timestamp: Date.now()
    });
  }

  scrollToBottom() {
    const messagesContainer = this.container.querySelector('#chat-messages');
    setTimeout(() => {
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }, 100);
  }

  autoResizeTextarea(textarea) {
    textarea.style.height = 'auto';
    const newHeight = Math.min(textarea.scrollHeight, 120); // 最大高度120px
    textarea.style.height = newHeight + 'px';
  }

  formatMessageContent(content) {
    // 简单的文本格式化
    return content
      .replace(/\n/g, '<br>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>');
  }

  formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (date.toDateString() === now.toDateString()) { // 今天
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleString('zh-CN', { 
        month: '2-digit', 
        day: '2-digit', 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    }
  }

  generateSessionId() {
    return 'chat_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  async saveChatHistory() {
    try {
      await chrome.storage.local.set({
        [`chat_history_${this.sessionId}`]: this.messages
      });
    } catch (error) {
      console.error('保存聊天记录失败:', error);
    }
  }

  async loadChatHistory() {
    try {
      const result = await chrome.storage.local.get([`chat_history_${this.sessionId}`]);
      const history = result[`chat_history_${this.sessionId}`];
      
      if (history && history.length > 0) {
        this.messages = history;
        this.updateMessagesDisplay();
        this.scrollToBottom();
      }
    } catch (error) {
      console.error('加载聊天记录失败:', error);
    }
  }

  async clearChatHistory() {
    try {
      await chrome.storage.local.remove([`chat_history_${this.sessionId}`]);
    } catch (error) {
      console.error('清除聊天记录失败:', error);
    }
  }

  addStyles() {
    if (document.getElementById('chat-component-styles')) return;

    const style = document.createElement('style');
    style.id = 'chat-component-styles';
    style.textContent = `
      .chat-component {
        height: 100%;
        display: flex;
        flex-direction: column;
        background: #fff;
      }

      .chat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #e0e0e0;
        background: #f8f9fa;
      }

      .chat-title {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .bot-avatar {
        font-size: 24px;
      }

      .title-info h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }

      .status {
        font-size: 12px;
        color: #4CAF50;
      }

      .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
        background: #fafafa;
      }

      .empty-chat {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #666;
        font-style: italic;
      }

      .message {
        display: flex;
        margin-bottom: 16px;
        align-items: flex-start;
        gap: 8px;
      }

      .user-message {
        flex-direction: row-reverse;
      }

      .message-avatar {
        font-size: 20px;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: #f0f0f0;
        flex-shrink: 0;
      }

      .message-content {
        max-width: 70%;
      }

      .user-message .message-content {
        text-align: right;
      }

      .message-bubble {
        padding: 12px 16px;
        border-radius: 18px;
        word-wrap: break-word;
        line-height: 1.4;
      }

      .bot-message .message-bubble {
        background: #fff;
        border: 1px solid #e0e0e0;
        color: #333;
      }

      .user-message .message-bubble {
        background: #4A90E2;
        color: white;
      }

      .message-time {
        font-size: 11px;
        color: #999;
        margin-top: 4px;
        padding: 0 4px;
      }

      .typing-dots {
        display: flex;
        gap: 4px;
        align-items: center;
      }

      .typing-dots span {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #999;
        animation: typing 1.4s infinite ease-in-out;
      }

      .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
      .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

      @keyframes typing {
        0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
        40% { transform: scale(1); opacity: 1; }
      }

      .quick-commands {
        padding: 12px 16px;
        border-top: 1px solid #e0e0e0;
        background: #fff;
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }

      .quick-cmd-btn {
        padding: 6px 12px;
        border: 1px solid #e0e0e0;
        border-radius: 16px;
        background: #f8f9fa;
        color: #666;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.2s ease;
      }

      .quick-cmd-btn:hover {
        background: #4A90E2;
        color: white;
        border-color: #4A90E2;
      }

      .chat-input-area {
        border-top: 1px solid #e0e0e0;
        background: #fff;
        padding: 16px;
      }

      .input-container {
        display: flex;
        gap: 8px;
        align-items: flex-end;
      }

      #chat-input {
        flex: 1;
        padding: 12px;
        border: 1px solid #e0e0e0;
        border-radius: 20px;
        resize: none;
        font-family: inherit;
        font-size: 14px;
        line-height: 1.4;
        max-height: 120px;
        overflow-y: auto;
      }

      #chat-input:focus {
        outline: none;
        border-color: #4A90E2;
        box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
      }

      .send-btn {
        width: 40px;
        height: 40px;
        border: none;
        border-radius: 50%;
        background: #4A90E2;
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        flex-shrink: 0;
      }

      .send-btn:hover:not(:disabled) {
        background: #357ABD;
        transform: scale(1.05);
      }

      .send-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
      }

      .input-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 8px;
        font-size: 11px;
        color: #999;
      }

      .action-btn {
        padding: 6px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        background: #fff;
        color: #666;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .action-btn:hover {
        background: #f8f9fa;
        color: #4A90E2;
      }
    `;

    document.head.appendChild(style);
  }
}

// 导出组件
if (typeof window !== 'undefined') {
  window.ChatComponent = ChatComponent;
}
