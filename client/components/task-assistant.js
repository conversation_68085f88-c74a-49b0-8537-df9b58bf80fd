// 任务助手组件
class TaskAssistant {
  constructor(container) {
    this.container = container;
    this.currentView = 'list'; // 'list' | 'create' | 'detail'
    this.tasks = [];
    this.taskTypes = [];
    this.filters = {
      status: '',
      date: '',
      name: ''
    };
    this.init();
  }

  async init() {
    await this.loadTaskTypes();
    this.render();
    this.bindEvents();
    await this.loadTasks();
  }

  render() {
    this.container.innerHTML = `
      <div class="task-assistant">
        <div class="task-header">
          <div class="task-nav">
            <button class="nav-btn ${this.currentView === 'list' ? 'active' : ''}" data-view="list">
              任务列表
            </button>
            <button class="nav-btn ${this.currentView === 'create' ? 'active' : ''}" data-view="create">
              发起新任务
            </button>
          </div>
          <button class="refresh-btn" id="refresh-tasks">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M8 3V1l3 3-3 3V5c-2.2 0-4 1.8-4 4s1.8 4 4 4 4-1.8 4-4h2c0 3.3-2.7 6-6 6s-6-2.7-6-6 2.7-6 6-6z"/>
            </svg>
          </button>
        </div>
        
        <div class="task-content">
          ${this.renderCurrentView()}
        </div>
      </div>
    `;

    this.addStyles();
  }

  renderCurrentView() {
    switch (this.currentView) {
      case 'list':
        return this.renderTaskList();
      case 'create':
        return this.renderCreateTask();
      case 'detail':
        return this.renderTaskDetail();
      default:
        return this.renderTaskList();
    }
  }

  renderTaskList() {
    return `
      <div class="task-list-view">
        <div class="task-filters">
          <input type="text" placeholder="搜索任务名称" class="filter-input" id="task-name-filter">
          <select class="filter-select" id="task-status-filter">
            <option value="">所有状态</option>
            <option value="pending">待执行</option>
            <option value="running">执行中</option>
            <option value="completed">已完成</option>
            <option value="failed">失败</option>
          </select>
          <input type="date" class="filter-input" id="task-date-filter">
        </div>
        
        <div class="task-list" id="task-list">
          ${this.renderTasks()}
        </div>
      </div>
    `;
  }

  renderCreateTask() {
    return `
      <div class="create-task-view">
        <form class="create-task-form" id="create-task-form">
          <div class="form-group">
            <label for="task-type">任务类型</label>
            <select id="task-type" class="form-select" required>
              <option value="">请选择任务类型</option>
              ${this.taskTypes.map(type => 
                `<option value="${type.value}">${type.label}</option>`
              ).join('')}
            </select>
          </div>
          
          <div class="form-group">
            <label for="task-name">任务名称</label>
            <input type="text" id="task-name" class="form-input" placeholder="请输入任务名称" required>
          </div>
          
          <div class="form-group" id="template-section" style="display: none;">
            <label>任务模板</label>
            <div class="template-actions">
              <button type="button" class="download-template-btn" id="download-template">
                下载模板
              </button>
              <span class="template-info">请下载模板文件，填写后上传</span>
            </div>
          </div>
          
          <div class="form-group" id="upload-section" style="display: none;">
            <label for="task-file">上传文件</label>
            <div class="file-upload">
              <input type="file" id="task-file" accept=".xlsx,.xls,.csv" class="file-input">
              <div class="file-upload-area" id="file-upload-area">
                <div class="upload-icon">📁</div>
                <div class="upload-text">点击选择文件或拖拽文件到此处</div>
                <div class="upload-hint">支持 Excel (.xlsx, .xls) 和 CSV 文件</div>
              </div>
            </div>
          </div>
          
          <div class="form-actions">
            <button type="button" class="cancel-btn" id="cancel-create">取消</button>
            <button type="submit" class="submit-btn">提交任务</button>
          </div>
        </form>
      </div>
    `;
  }

  renderTasks() {
    if (this.tasks.length === 0) {
      return `
        <div class="empty-state">
          <div class="empty-icon">📋</div>
          <div class="empty-text">暂无任务</div>
          <button class="create-first-task-btn" data-view="create">创建第一个任务</button>
        </div>
      `;
    }

    return this.tasks.map(task => `
      <div class="task-item" data-task-id="${task.id}">
        <div class="task-main">
          <div class="task-info">
            <h4 class="task-title">${task.name}</h4>
            <div class="task-meta">
              <span class="task-id">编号: ${task.id}</span>
              <span class="task-type">${task.type}</span>
              <span class="task-time">${this.formatTime(task.createTime)}</span>
            </div>
          </div>
          <div class="task-status">
            <span class="status-badge ${task.status}">${this.getStatusText(task.status)}</span>
          </div>
        </div>
        <div class="task-actions">
          <button class="action-btn view-btn" onclick="taskAssistant.viewTaskDetail('${task.id}')">
            查看详情
          </button>
          ${task.status === 'pending' ? 
            `<button class="action-btn cancel-btn" onclick="taskAssistant.cancelTask('${task.id}')">取消</button>` : 
            ''
          }
        </div>
      </div>
    `).join('');
  }

  bindEvents() {
    // 导航切换
    this.container.addEventListener('click', (e) => {
      if (e.target.classList.contains('nav-btn')) {
        const view = e.target.dataset.view;
        this.switchView(view);
      }
    });

    // 刷新按钮
    this.container.addEventListener('click', (e) => {
      if (e.target.closest('#refresh-tasks')) {
        this.loadTasks();
      }
    });

    // 过滤器
    this.container.addEventListener('input', (e) => {
      if (e.target.id === 'task-name-filter') {
        this.filters.name = e.target.value;
        this.filterTasks();
      }
    });

    this.container.addEventListener('change', (e) => {
      if (e.target.id === 'task-status-filter') {
        this.filters.status = e.target.value;
        this.filterTasks();
      }
      if (e.target.id === 'task-date-filter') {
        this.filters.date = e.target.value;
        this.filterTasks();
      }
      if (e.target.id === 'task-type') {
        this.handleTaskTypeChange(e.target.value);
      }
    });

    // 表单提交
    this.container.addEventListener('submit', (e) => {
      if (e.target.id === 'create-task-form') {
        e.preventDefault();
        this.submitTask();
      }
    });

    // 文件上传
    this.container.addEventListener('change', (e) => {
      if (e.target.id === 'task-file') {
        this.handleFileSelect(e.target.files[0]);
      }
    });
  }

  switchView(view) {
    this.currentView = view;
    this.render();
    this.bindEvents();
  }

  async loadTaskTypes() {
    try {
      // 模拟从服务器加载任务类型
      this.taskTypes = [
        { value: 'data_import', label: '数据导入' },
        { value: 'report_generate', label: '报表生成' },
        { value: 'batch_process', label: '批量处理' },
        { value: 'sync_data', label: '数据同步' }
      ];
    } catch (error) {
      console.error('加载任务类型失败:', error);
    }
  }

  async loadTasks() {
    try {
      // 模拟从服务器加载任务列表
      this.tasks = [
        {
          id: 'T001',
          name: '客户数据导入',
          type: '数据导入',
          status: 'completed',
          createTime: new Date(Date.now() - 86400000).toISOString()
        },
        {
          id: 'T002', 
          name: '月度销售报表',
          type: '报表生成',
          status: 'running',
          createTime: new Date(Date.now() - 3600000).toISOString()
        }
      ];

      this.updateTaskList();
    } catch (error) {
      console.error('加载任务失败:', error);
    }
  }

  updateTaskList() {
    const taskListElement = this.container.querySelector('#task-list');
    if (taskListElement) {
      taskListElement.innerHTML = this.renderTasks();
    }
  }

  filterTasks() {
    // 实现任务过滤逻辑
    this.updateTaskList();
  }

  handleTaskTypeChange(taskType) {
    const templateSection = this.container.querySelector('#template-section');
    const uploadSection = this.container.querySelector('#upload-section');
    
    if (taskType) {
      templateSection.style.display = 'block';
      uploadSection.style.display = 'block';
    } else {
      templateSection.style.display = 'none';
      uploadSection.style.display = 'none';
    }
  }

  async submitTask() {
    const formData = new FormData(this.container.querySelector('#create-task-form'));
    
    try {
      // 提交任务到服务器
      console.log('提交任务:', formData);
      
      // 模拟成功响应
      this.showMessage('任务提交成功', 'success');
      this.switchView('list');
      this.loadTasks();
    } catch (error) {
      console.error('提交任务失败:', error);
      this.showMessage('提交任务失败', 'error');
    }
  }

  getStatusText(status) {
    const statusMap = {
      pending: '待执行',
      running: '执行中', 
      completed: '已完成',
      failed: '失败'
    };
    return statusMap[status] || status;
  }

  formatTime(timeString) {
    const date = new Date(timeString);
    return date.toLocaleString('zh-CN');
  }

  showMessage(text, type) {
    // 显示消息提示
    console.log(`${type}: ${text}`);
  }

  addStyles() {
    if (document.getElementById('task-assistant-styles')) return;

    const style = document.createElement('style');
    style.id = 'task-assistant-styles';
    style.textContent = `
      .task-assistant {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .task-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #e0e0e0;
      }

      .task-nav {
        display: flex;
        gap: 8px;
      }

      .nav-btn {
        padding: 8px 16px;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        background: #fff;
        color: #666;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.2s ease;
      }

      .nav-btn:hover {
        border-color: #4A90E2;
        color: #4A90E2;
      }

      .nav-btn.active {
        background: #4A90E2;
        color: white;
        border-color: #4A90E2;
      }

      .refresh-btn {
        padding: 8px;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        background: #fff;
        color: #666;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .refresh-btn:hover {
        background: #f8f9fa;
        color: #4A90E2;
      }

      .task-content {
        flex: 1;
        overflow-y: auto;
      }

      .task-filters {
        display: flex;
        gap: 12px;
        margin-bottom: 16px;
      }

      .filter-input,
      .filter-select {
        flex: 1;
        padding: 8px 12px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        font-size: 14px;
      }

      .task-item {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        background: #fff;
        transition: box-shadow 0.2s ease;
      }

      .task-item:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .task-main {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
      }

      .task-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
      }

      .task-meta {
        display: flex;
        gap: 12px;
        font-size: 12px;
        color: #666;
      }

      .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
      }

      .status-badge.pending {
        background: #FFF3E0;
        color: #F57C00;
      }

      .status-badge.running {
        background: #E3F2FD;
        color: #1976D2;
      }

      .status-badge.completed {
        background: #E8F5E8;
        color: #388E3C;
      }

      .status-badge.failed {
        background: #FFEBEE;
        color: #D32F2F;
      }

      .task-actions {
        display: flex;
        gap: 8px;
      }

      .action-btn {
        padding: 6px 12px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        background: #fff;
        color: #666;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.2s ease;
      }

      .action-btn:hover {
        background: #f8f9fa;
        border-color: #4A90E2;
        color: #4A90E2;
      }

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60px 20px;
        text-align: center;
        color: #666;
      }

      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
      }

      .empty-text {
        font-size: 16px;
        margin-bottom: 20px;
      }

      .create-first-task-btn {
        padding: 12px 24px;
        background: #4A90E2;
        color: white;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: background 0.2s ease;
      }

      .create-first-task-btn:hover {
        background: #357ABD;
      }

      .form-group {
        margin-bottom: 20px;
      }

      .form-group label {
        display: block;
        font-weight: 500;
        color: #333;
        margin-bottom: 6px;
      }

      .form-input,
      .form-select {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        font-size: 14px;
      }

      .form-actions {
        display: flex;
        gap: 12px;
        justify-content: flex-end;
        margin-top: 24px;
        padding-top: 20px;
        border-top: 1px solid #e0e0e0;
      }

      .cancel-btn,
      .submit-btn {
        padding: 10px 20px;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s ease;
      }

      .cancel-btn {
        background: #fff;
        color: #666;
      }

      .cancel-btn:hover {
        background: #f8f9fa;
      }

      .submit-btn {
        background: #4A90E2;
        color: white;
        border-color: #4A90E2;
      }

      .submit-btn:hover {
        background: #357ABD;
      }
    `;

    document.head.appendChild(style);
  }

  // 全局方法
  viewTaskDetail(taskId) {
    console.log('查看任务详情:', taskId);
  }

  cancelTask(taskId) {
    console.log('取消任务:', taskId);
  }
}

// 导出组件
if (typeof window !== 'undefined') {
  window.TaskAssistant = TaskAssistant;
}
