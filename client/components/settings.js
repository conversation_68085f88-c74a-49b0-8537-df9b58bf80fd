// 设置组件
class SettingsComponent {
  constructor(container) {
    this.container = container;
    this.settings = {};
    this.targetSites = [];
    this.isDirty = false;
    this.init();
  }

  async init() {
    await this.loadSettings();
    await this.loadTargetSites();
    this.render();
    this.bindEvents();
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.local.get(['settings']);
      this.settings = result.settings || {
        serverAddress: 'ws://localhost:8080',
        targetSites: [],
        ssoUrls: {},
        desktopNotifications: true,
        soundAlerts: true,
        autoLogin: true,
        autoSubmitCookies: true,
        checkInterval: 30
      };
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  }

  async loadTargetSites() {
    try {
      // 模拟从服务器获取可用的目标站点
      this.targetSites = [
        { value: 'erp.example.com', label: '积理ERP系统', sso: 'https://sso.example.com/erp' },
        { value: 'wanshang.example.com', label: '万商管理系统', sso: 'https://sso.example.com/wanshang' },
        { value: 'haibo.example.com', label: '海博业务系统', sso: 'https://sso.example.com/haibo' },
        { value: 'crm.example.com', label: 'CRM客户系统', sso: 'https://sso.example.com/crm' }
      ];
    } catch (error) {
      console.error('加载目标站点失败:', error);
      this.targetSites = [];
    }
  }

  render() {
    this.container.innerHTML = `
      <div class="settings-component">
        <div class="settings-header">
          <h3>系统设置</h3>
          <div class="header-actions">
            <button class="test-btn" id="test-connection">测试连接</button>
            <button class="save-btn" id="save-settings" ${!this.isDirty ? 'disabled' : ''}>
              保存设置
            </button>
          </div>
        </div>

        <form class="settings-form" id="settings-form">
          <div class="settings-section">
            <h4>服务器配置</h4>
            <div class="form-group">
              <label for="server-address">服务器地址</label>
              <input 
                type="text" 
                id="server-address" 
                class="form-input" 
                placeholder="ws://localhost:8080"
                value="${this.settings.serverAddress || ''}"
                required
              >
              <small class="form-help">WebSocket服务器地址，用于实时通信</small>
            </div>
          </div>

          <div class="settings-section">
            <h4>代理站点配置</h4>
            <div class="form-group">
              <label>选择代理站点</label>
              <div class="site-list">
                ${this.renderTargetSites()}
              </div>
              <small class="form-help">选择需要代理操作的目标系统</small>
            </div>
          </div>

          <div class="settings-section">
            <h4>通知设置</h4>
            <div class="form-group">
              <div class="checkbox-group">
                <label class="checkbox-item">
                  <input 
                    type="checkbox" 
                    id="desktop-notifications"
                    ${this.settings.desktopNotifications ? 'checked' : ''}
                  >
                  <span class="checkmark"></span>
                  <span class="checkbox-label">桌面通知</span>
                </label>
                <small class="checkbox-help">任务完成或出现错误时显示桌面通知</small>
              </div>

              <div class="checkbox-group">
                <label class="checkbox-item">
                  <input 
                    type="checkbox" 
                    id="sound-alerts"
                    ${this.settings.soundAlerts ? 'checked' : ''}
                  >
                  <span class="checkmark"></span>
                  <span class="checkbox-label">声音提醒</span>
                </label>
                <small class="checkbox-help">重要事件发生时播放提示音</small>
              </div>
            </div>
          </div>

          <div class="settings-section">
            <h4>自动化设置</h4>
            <div class="form-group">
              <div class="checkbox-group">
                <label class="checkbox-item">
                  <input 
                    type="checkbox" 
                    id="auto-login"
                    ${this.settings.autoLogin ? 'checked' : ''}
                  >
                  <span class="checkmark"></span>
                  <span class="checkbox-label">自动检测登录状态</span>
                </label>
                <small class="checkbox-help">自动检测目标站点的登录状态变化</small>
              </div>

              <div class="checkbox-group">
                <label class="checkbox-item">
                  <input 
                    type="checkbox" 
                    id="auto-submit-cookies"
                    ${this.settings.autoSubmitCookies ? 'checked' : ''}
                  >
                  <span class="checkmark"></span>
                  <span class="checkbox-label">自动提交Cookie</span>
                </label>
                <small class="checkbox-help">登录后自动将Cookie提交到服务器</small>
              </div>
            </div>

            <div class="form-group">
              <label for="check-interval">检查间隔（秒）</label>
              <input 
                type="number" 
                id="check-interval" 
                class="form-input" 
                min="10" 
                max="300" 
                value="${this.settings.checkInterval || 30}"
              >
              <small class="form-help">登录状态检查间隔时间，建议30-60秒</small>
            </div>
          </div>

          <div class="settings-section">
            <h4>高级设置</h4>
            <div class="form-group">
              <label for="log-level">日志级别</label>
              <select id="log-level" class="form-select">
                <option value="error" ${this.settings.logLevel === 'error' ? 'selected' : ''}>错误</option>
                <option value="warn" ${this.settings.logLevel === 'warn' ? 'selected' : ''}>警告</option>
                <option value="info" ${this.settings.logLevel === 'info' ? 'selected' : ''}>信息</option>
                <option value="debug" ${this.settings.logLevel === 'debug' ? 'selected' : ''}>调试</option>
              </select>
              <small class="form-help">控制台日志输出级别</small>
            </div>

            <div class="form-group">
              <div class="checkbox-group">
                <label class="checkbox-item">
                  <input 
                    type="checkbox" 
                    id="debug-mode"
                    ${this.settings.debugMode ? 'checked' : ''}
                  >
                  <span class="checkmark"></span>
                  <span class="checkbox-label">调试模式</span>
                </label>
                <small class="checkbox-help">启用详细的调试信息输出</small>
              </div>
            </div>
          </div>

          <div class="settings-section">
            <h4>数据管理</h4>
            <div class="form-group">
              <div class="data-actions">
                <button type="button" class="action-btn" id="export-settings">
                  导出设置
                </button>
                <button type="button" class="action-btn" id="import-settings">
                  导入设置
                </button>
                <button type="button" class="action-btn danger" id="reset-settings">
                  重置设置
                </button>
              </div>
              <small class="form-help">备份、恢复或重置所有设置</small>
            </div>
          </div>
        </form>

        <div class="settings-footer">
          <div class="version-info">
            <span>版本 1.0.0</span>
            <span class="separator">|</span>
            <a href="#" id="help-link">使用帮助</a>
            <span class="separator">|</span>
            <a href="#" id="feedback-link">意见反馈</a>
          </div>
        </div>
      </div>
    `;

    this.addStyles();
  }

  renderTargetSites() {
    return this.targetSites.map(site => {
      const isSelected = this.settings.targetSites?.includes(site.value);
      const ssoUrl = this.settings.ssoUrls?.[site.value] || site.sso;
      
      return `
        <div class="site-item">
          <label class="site-checkbox">
            <input 
              type="checkbox" 
              class="site-check" 
              data-site="${site.value}"
              ${isSelected ? 'checked' : ''}
            >
            <span class="checkmark"></span>
            <span class="site-label">${site.label}</span>
          </label>
          <div class="site-config ${isSelected ? 'visible' : ''}">
            <label class="sso-label">SSO登录地址:</label>
            <input 
              type="url" 
              class="sso-input" 
              data-site="${site.value}"
              value="${ssoUrl}"
              placeholder="https://sso.example.com/login"
            >
          </div>
        </div>
      `;
    }).join('');
  }

  bindEvents() {
    // 表单变化监听
    this.container.addEventListener('input', (e) => {
      this.markDirty();
    });

    this.container.addEventListener('change', (e) => {
      this.markDirty();
      
      // 站点选择变化
      if (e.target.classList.contains('site-check')) {
        this.handleSiteToggle(e.target);
      }
    });

    // 保存设置
    this.container.addEventListener('click', (e) => {
      if (e.target.id === 'save-settings') {
        this.saveSettings();
      }
    });

    // 测试连接
    this.container.addEventListener('click', (e) => {
      if (e.target.id === 'test-connection') {
        this.testConnection();
      }
    });

    // 数据管理操作
    this.container.addEventListener('click', (e) => {
      switch (e.target.id) {
        case 'export-settings':
          this.exportSettings();
          break;
        case 'import-settings':
          this.importSettings();
          break;
        case 'reset-settings':
          this.resetSettings();
          break;
        case 'help-link':
          e.preventDefault();
          this.openHelp();
          break;
        case 'feedback-link':
          e.preventDefault();
          this.openFeedback();
          break;
      }
    });

    // 阻止表单提交
    this.container.addEventListener('submit', (e) => {
      e.preventDefault();
      this.saveSettings();
    });
  }

  handleSiteToggle(checkbox) {
    const siteValue = checkbox.dataset.site;
    const siteConfig = checkbox.closest('.site-item').querySelector('.site-config');
    
    if (checkbox.checked) {
      siteConfig.classList.add('visible');
    } else {
      siteConfig.classList.remove('visible');
    }
  }

  markDirty() {
    if (!this.isDirty) {
      this.isDirty = true;
      const saveBtn = this.container.querySelector('#save-settings');
      if (saveBtn) {
        saveBtn.disabled = false;
      }
    }
  }

  async saveSettings() {
    try {
      const formData = this.collectFormData();
      
      // 验证设置
      if (!this.validateSettings(formData)) {
        return;
      }

      // 保存到存储
      await chrome.storage.local.set({ settings: formData });
      
      // 通知background script更新设置
      chrome.runtime.sendMessage({
        type: 'UPDATE_SETTINGS',
        settings: formData
      });

      this.settings = formData;
      this.isDirty = false;
      
      const saveBtn = this.container.querySelector('#save-settings');
      if (saveBtn) {
        saveBtn.disabled = true;
      }

      this.showMessage('设置已保存', 'success');
    } catch (error) {
      console.error('保存设置失败:', error);
      this.showMessage('保存设置失败', 'error');
    }
  }

  collectFormData() {
    const form = this.container.querySelector('#settings-form');
    const formData = new FormData(form);
    
    // 收集选中的站点
    const selectedSites = [];
    const ssoUrls = {};
    
    this.container.querySelectorAll('.site-check:checked').forEach(checkbox => {
      const siteValue = checkbox.dataset.site;
      selectedSites.push(siteValue);
      
      const ssoInput = this.container.querySelector(`.sso-input[data-site="${siteValue}"]`);
      if (ssoInput && ssoInput.value) {
        ssoUrls[siteValue] = ssoInput.value;
      }
    });

    return {
      serverAddress: this.container.querySelector('#server-address').value,
      targetSites: selectedSites,
      ssoUrls: ssoUrls,
      desktopNotifications: this.container.querySelector('#desktop-notifications').checked,
      soundAlerts: this.container.querySelector('#sound-alerts').checked,
      autoLogin: this.container.querySelector('#auto-login').checked,
      autoSubmitCookies: this.container.querySelector('#auto-submit-cookies').checked,
      checkInterval: parseInt(this.container.querySelector('#check-interval').value),
      logLevel: this.container.querySelector('#log-level').value,
      debugMode: this.container.querySelector('#debug-mode').checked
    };
  }

  validateSettings(settings) {
    if (!settings.serverAddress) {
      this.showMessage('请输入服务器地址', 'error');
      return false;
    }

    if (!settings.serverAddress.startsWith('ws://') && !settings.serverAddress.startsWith('wss://')) {
      this.showMessage('服务器地址必须以 ws:// 或 wss:// 开头', 'error');
      return false;
    }

    if (settings.checkInterval < 10 || settings.checkInterval > 300) {
      this.showMessage('检查间隔必须在10-300秒之间', 'error');
      return false;
    }

    return true;
  }

  async testConnection() {
    const serverAddress = this.container.querySelector('#server-address').value;
    
    if (!serverAddress) {
      this.showMessage('请先输入服务器地址', 'error');
      return;
    }

    const testBtn = this.container.querySelector('#test-connection');
    const originalText = testBtn.textContent;
    
    testBtn.textContent = '测试中...';
    testBtn.disabled = true;

    try {
      // 测试WebSocket连接
      const ws = new WebSocket(serverAddress);
      
      const result = await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          ws.close();
          reject(new Error('连接超时'));
        }, 10000);

        ws.onopen = () => {
          clearTimeout(timeout);
          ws.close();
          resolve(true);
        };

        ws.onerror = () => {
          clearTimeout(timeout);
          reject(new Error('连接失败'));
        };
      });

      this.showMessage('连接测试成功', 'success');
    } catch (error) {
      console.error('连接测试失败:', error);
      this.showMessage(`连接测试失败: ${error.message}`, 'error');
    } finally {
      testBtn.textContent = originalText;
      testBtn.disabled = false;
    }
  }

  exportSettings() {
    const dataStr = JSON.stringify(this.settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `apa-settings-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    this.showMessage('设置已导出', 'success');
  }

  importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    
    input.onchange = async (e) => {
      const file = e.target.files[0];
      if (!file) return;
      
      try {
        const text = await file.text();
        const importedSettings = JSON.parse(text);
        
        // 验证导入的设置
        if (this.validateSettings(importedSettings)) {
          this.settings = importedSettings;
          this.render();
          this.bindEvents();
          this.markDirty();
          this.showMessage('设置已导入', 'success');
        }
      } catch (error) {
        console.error('导入设置失败:', error);
        this.showMessage('导入设置失败，请检查文件格式', 'error');
      }
    };
    
    input.click();
  }

  resetSettings() {
    if (confirm('确定要重置所有设置吗？此操作不可撤销。')) {
      this.settings = {
        serverAddress: 'ws://localhost:8080',
        targetSites: [],
        ssoUrls: {},
        desktopNotifications: true,
        soundAlerts: true,
        autoLogin: true,
        autoSubmitCookies: true,
        checkInterval: 30,
        logLevel: 'info',
        debugMode: false
      };
      
      this.render();
      this.bindEvents();
      this.markDirty();
      this.showMessage('设置已重置', 'success');
    }
  }

  openHelp() {
    chrome.tabs.create({
      url: 'https://example.com/help'
    });
  }

  openFeedback() {
    chrome.tabs.create({
      url: 'https://example.com/feedback'
    });
  }

  showMessage(text, type = 'info') {
    // 创建消息提示
    const message = document.createElement('div');
    message.className = `message message-${type}`;
    message.textContent = text;
    message.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 6px;
      color: white;
      font-weight: 500;
      z-index: 10000;
      animation: slideIn 0.3s ease-out;
    `;

    switch (type) {
      case 'success':
        message.style.background = '#4CAF50';
        break;
      case 'error':
        message.style.background = '#F44336';
        break;
      case 'warning':
        message.style.background = '#FF9800';
        break;
      default:
        message.style.background = '#2196F3';
    }

    document.body.appendChild(message);

    setTimeout(() => {
      message.remove();
    }, 3000);
  }

  addStyles() {
    if (document.getElementById('settings-component-styles')) return;

    const style = document.createElement('style');
    style.id = 'settings-component-styles';
    style.textContent = `
      .settings-component {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .settings-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid #e0e0e0;
      }

      .settings-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      .header-actions {
        display: flex;
        gap: 12px;
      }

      .test-btn,
      .save-btn {
        padding: 8px 16px;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s ease;
      }

      .test-btn {
        background: #fff;
        color: #666;
      }

      .test-btn:hover {
        background: #f8f9fa;
        border-color: #4A90E2;
        color: #4A90E2;
      }

      .save-btn {
        background: #4A90E2;
        color: white;
        border-color: #4A90E2;
      }

      .save-btn:hover:not(:disabled) {
        background: #357ABD;
      }

      .save-btn:disabled {
        background: #ccc;
        border-color: #ccc;
        cursor: not-allowed;
      }

      .settings-form {
        flex: 1;
        overflow-y: auto;
      }

      .settings-section {
        margin-bottom: 32px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
      }

      .settings-section h4 {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        border-bottom: 1px solid #e0e0e0;
        padding-bottom: 8px;
      }

      .form-group {
        margin-bottom: 20px;
      }

      .form-group:last-child {
        margin-bottom: 0;
      }

      .form-group label {
        display: block;
        font-weight: 500;
        color: #333;
        margin-bottom: 6px;
      }

      .form-input,
      .form-select {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        font-size: 14px;
        background: #fff;
        transition: border-color 0.2s ease;
      }

      .form-input:focus,
      .form-select:focus {
        outline: none;
        border-color: #4A90E2;
        box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
      }

      .form-help {
        display: block;
        font-size: 12px;
        color: #666;
        margin-top: 4px;
      }

      .checkbox-group {
        margin-bottom: 16px;
      }

      .checkbox-item {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        margin-bottom: 8px;
      }

      .checkbox-item input[type="checkbox"] {
        margin: 0;
      }

      .checkbox-help {
        font-size: 12px;
        color: #666;
        margin-left: 24px;
        margin-top: -4px;
        margin-bottom: 8px;
      }

      .site-list {
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        background: #fff;
        overflow: hidden;
      }

      .site-item {
        border-bottom: 1px solid #e0e0e0;
        padding: 16px;
      }

      .site-item:last-child {
        border-bottom: none;
      }

      .site-checkbox {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        font-weight: 500;
      }

      .site-config {
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid #f0f0f0;
        display: none;
      }

      .site-config.visible {
        display: block;
      }

      .sso-label {
        display: block;
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }

      .sso-input {
        width: 100%;
        padding: 8px 10px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        font-size: 13px;
      }

      .data-actions {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
      }

      .action-btn {
        padding: 8px 16px;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        background: #fff;
        color: #666;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.2s ease;
      }

      .action-btn:hover {
        background: #f8f9fa;
        border-color: #4A90E2;
        color: #4A90E2;
      }

      .action-btn.danger {
        color: #F44336;
      }

      .action-btn.danger:hover {
        border-color: #F44336;
        color: #F44336;
      }

      .settings-footer {
        margin-top: 20px;
        padding-top: 16px;
        border-top: 1px solid #e0e0e0;
        text-align: center;
      }

      .version-info {
        font-size: 12px;
        color: #666;
      }

      .version-info a {
        color: #4A90E2;
        text-decoration: none;
      }

      .version-info a:hover {
        text-decoration: underline;
      }

      .separator {
        margin: 0 8px;
        color: #ccc;
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateX(20px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }
    `;

    document.head.appendChild(style);
  }
}

// 导出组件
if (typeof window !== 'undefined') {
  window.SettingsComponent = SettingsComponent;
}
