# 快速启动指南

## 解决图标问题

当前插件缺少PNG图标文件，导致Chrome显示错误。以下是快速解决方案：

### 方法1：生成图标（推荐）

1. **打开图标生成器**
   ```
   在浏览器中打开：client/assets/icons/generate-icons.html
   ```

2. **下载图标**
   - 点击页面上的"下载所有图标"按钮
   - 浏览器会自动下载4个PNG文件：
     - icon16.png
     - icon32.png  
     - icon48.png
     - icon128.png

3. **放置图标文件**
   - 将下载的PNG文件放到 `client/assets/icons/` 目录下

4. **恢复图标配置**
   - 编辑 `client/manifest.json`
   - 在 `"action"` 部分添加：
   ```json
   "default_icon": {
     "16": "assets/icons/icon16.png",
     "32": "assets/icons/icon32.png",
     "48": "assets/icons/icon48.png", 
     "128": "assets/icons/icon128.png"
   }
   ```
   - 在根级别添加：
   ```json
   "icons": {
     "16": "assets/icons/icon16.png",
     "32": "assets/icons/icon32.png",
     "48": "assets/icons/icon48.png",
     "128": "assets/icons/icon128.png"
   }
   ```

5. **重新加载插件**
   - 在Chrome扩展程序页面点击"重新加载"

### 方法2：临时跳过图标

如果暂时不需要自定义图标，可以继续使用当前配置（已移除图标引用），Chrome会使用默认图标。

## 安装和测试插件

### 1. 安装插件

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `client` 目录
6. 插件安装完成

### 2. 测试功能

1. **测试悬浮图标**
   - 访问任意网站
   - 查看页面右侧是否出现蓝色机器人头像

2. **测试助手面板**
   - 点击悬浮图标
   - 查看是否弹出助手面板
   - 测试三个标签页切换

3. **测试设置功能**
   - 切换到"设置"标签页
   - 配置服务器地址（如：ws://localhost:8080）
   - 点击"测试连接"按钮

### 3. 配置服务器

插件需要连接到WebSocket服务器才能正常工作：

1. **设置服务器地址**
   - 在设置页面输入服务器地址
   - 格式：`ws://your-server:port` 或 `wss://your-server:port`

2. **配置代理站点**
   - 选择需要代理的目标网站
   - 设置对应的SSO登录地址

3. **保存设置**
   - 点击"保存设置"按钮
   - 插件会自动尝试连接服务器

## 开发和调试

### 调试方法

1. **Content Script调试**
   - 在目标网页按F12打开开发者工具
   - 在Console中查看插件日志

2. **Background Script调试**
   - 在扩展程序页面点击插件的"详细信息"
   - 点击"检查视图 background page"

3. **Popup调试**
   - 右键点击插件图标
   - 选择"检查弹出内容"

### 常见问题

1. **悬浮图标不显示**
   - 检查Console是否有错误
   - 确认插件已正确加载
   - 刷新页面重试

2. **无法连接服务器**
   - 检查服务器地址是否正确
   - 确认服务器正在运行
   - 检查网络连接

3. **样式显示异常**
   - 检查CSS文件是否正确加载
   - 查看是否与目标网站样式冲突

## 项目结构

```
client/
├── manifest.json              # 插件配置
├── background.js             # 后台脚本
├── content.js               # 内容脚本
├── popup/                   # 弹窗界面
│   ├── popup.html
│   ├── popup.css
│   └── popup.js
├── components/              # 功能组件
│   ├── task-assistant.js
│   ├── chat.js
│   └── settings.js
├── utils/                   # 工具类
│   ├── websocket.js
│   ├── http.js
│   ├── cookie-manager.js
│   └── auth.js
└── assets/                  # 静态资源
    ├── css/
    └── icons/
```

## 下一步

1. **完善服务器端**：实现WebSocket服务器和HTTP API
2. **测试功能**：在实际环境中测试各项功能
3. **优化体验**：根据使用反馈优化界面和交互
4. **添加功能**：根据需求添加新的功能模块

## 技术支持

如遇到问题，请检查：
1. Chrome版本是否支持（建议88+）
2. 插件权限是否正确配置
3. 服务器是否正常运行
4. 网络连接是否正常

更多详细信息请参考 `README.md` 文件。
