{"name": "b-assistant-apa-client", "version": "1.0.0", "description": "B端智能助手-APA 客户端插件，Chrome浏览器扩展，用于与服务端配合实现RPA自动化任务编排", "main": "background.js", "scripts": {"build": "echo 'Building Chrome extension...' && npm run validate", "validate": "node scripts/validate-manifest.js", "pack": "echo 'Packing extension...' && npm run build", "dev": "echo 'Development mode - load unpacked extension in Chrome'", "lint": "echo 'Linting JavaScript files...'", "test": "echo 'Running tests...'", "clean": "echo 'Cleaning build artifacts...'"}, "keywords": ["chrome-extension", "rpa", "automation", "assistant", "browser-plugin", "websocket", "task-management"], "author": "B端智能助手开发团队", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/b-assistant-apa.git"}, "bugs": {"url": "https://github.com/your-org/b-assistant-apa/issues"}, "homepage": "https://github.com/your-org/b-assistant-apa#readme", "devDependencies": {"eslint": "^8.0.0", "prettier": "^2.0.0"}, "engines": {"node": ">=14.0.0"}, "browserslist": ["Chrome >= 88"], "manifest": {"version": "1.0.0", "permissions": ["activeTab", "storage", "cookies", "tabs", "scripting", "notifications"], "host_permissions": ["http://*/*", "https://*/*"]}, "files": ["manifest.json", "background.js", "content.js", "popup/", "components/", "utils/", "assets/", "README.md"]}