# B端智能助手-APA 客户端插件

## 概述

B端智能助手-APA 是一个Chrome浏览器插件，与服务端相结合，在服务端实现RPA，做操作流程的自动化任务编排。本插件作为客户端组件，负责捕获用户会话、注入监控脚本、转发数据等功能。

## 功能特性

### 🎯 核心功能
- **悬浮图标**: 在支持的目标网站显示右侧悬浮的圆形机器人头像
- **智能助手面板**: 包含任务管理、智能对话、系统设置三大功能模块
- **自动登录检测**: 自动检测目标系统的登录状态
- **Cookie管理**: 自动采集和提交Cookie到服务端
- **实时通信**: 通过WebSocket与服务端进行实时数据交换

### 📋 任务管理
- **发起新任务**: 支持多种任务类型，提供Excel模板下载和文件上传
- **任务列表**: 查看任务状态、进度和详细信息
- **任务过滤**: 按状态、时间、名称等条件筛选任务

### 💬 智能对话
- **实时聊天**: 与智能助手进行对话交流
- **快捷命令**: 预设常用命令快速执行操作
- **流式回复**: 支持大模型流式对话展示

### ⚙️ 系统设置
- **服务器配置**: 设置WebSocket服务器地址
- **代理站点**: 配置需要代理的目标系统
- **SSO配置**: 设置各站点的单点登录地址
- **通知设置**: 桌面通知和声音提醒配置
- **自动化选项**: 自动登录检测、Cookie提交等

## 安装方法

### 开发模式安装
1. 打开Chrome浏览器，进入扩展程序管理页面 (`chrome://extensions/`)
2. 开启"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择本项目的 `client` 目录
5. 插件安装完成，可在扩展程序列表中看到"B端智能助手-APA"

### 打包安装
1. 在扩展程序管理页面点击"打包扩展程序"
2. 选择 `client` 目录进行打包
3. 生成 `.crx` 文件后可分发安装

## 使用指南

### 首次使用
1. 安装插件后，访问支持的目标网站
2. 页面右侧会出现蓝色的机器人头像悬浮图标
3. 点击悬浮图标打开助手面板
4. 在设置页面配置服务器地址和代理站点

### 任务管理
1. 切换到"辅助任务"标签页
2. 点击"发起新任务"创建新任务
3. 选择任务类型，下载并填写Excel模板
4. 上传填写好的文件，提交任务
5. 在任务列表中查看任务状态和进度

### 智能对话
1. 切换到"智能对话"标签页
2. 在输入框中输入问题或指令
3. 使用快捷命令快速执行常用操作
4. 查看助手的回复和建议

### 系统设置
1. 切换到"设置"标签页
2. 配置服务器地址（WebSocket地址）
3. 选择需要代理的目标站点
4. 设置各站点的SSO登录地址
5. 调整通知和自动化选项
6. 点击"保存设置"应用配置

## 技术架构

### 文件结构
```
client/
├── manifest.json           # Chrome插件配置文件
├── background.js           # 后台服务脚本
├── content.js             # 内容脚本
├── popup/                 # 插件弹窗界面
│   ├── popup.html
│   ├── popup.css
│   └── popup.js
├── components/            # 功能组件
│   ├── task-assistant.js  # 任务助手组件
│   ├── chat.js           # 智能对话组件
│   └── settings.js       # 设置组件
├── utils/                # 工具类
│   ├── websocket.js      # WebSocket通信
│   ├── http.js           # HTTP请求
│   ├── cookie-manager.js # Cookie管理
│   └── auth.js           # 认证管理
└── assets/               # 静态资源
    ├── css/
    └── icons/
```

### 核心技术
- **Chrome Extension API**: 插件开发框架
- **WebSocket**: 实时通信协议
- **JavaScript ES6+**: 现代JavaScript语法
- **CSS3**: 现代样式和动画
- **Chrome Storage API**: 本地数据存储
- **Chrome Cookies API**: Cookie管理

### 通信协议
- **WebSocket**: 用于实时消息通知、聊天对话
- **HTTP/HTTPS**: 用于任务创建、查询等API调用
- **Chrome Runtime Messages**: 插件内部组件通信

## 开发说明

### 开发环境
- Chrome浏览器 88+
- 现代代码编辑器（推荐VS Code）
- 基本的HTML/CSS/JavaScript知识

### 调试方法
1. 在Chrome扩展程序页面点击插件的"详细信息"
2. 开启"收集错误"选项
3. 使用Chrome开发者工具调试content script
4. 查看background页面的控制台输出

### 自定义开发
1. 修改 `manifest.json` 中的权限和配置
2. 在 `components/` 目录下添加新的功能组件
3. 在 `utils/` 目录下添加工具类
4. 更新 `content.js` 集成新组件

## 配置说明

### 服务器配置
- **服务器地址**: WebSocket服务器的完整地址，格式为 `ws://host:port` 或 `wss://host:port`
- **连接超时**: 默认10秒，可在代码中调整

### 代理站点配置
- **站点域名**: 需要代理的目标系统域名
- **SSO地址**: 对应的单点登录页面地址
- **Cookie检测**: 自动检测登录相关的Cookie

### 通知配置
- **桌面通知**: 任务完成或错误时显示系统通知
- **声音提醒**: 重要事件的声音提示
- **检查间隔**: 登录状态检查的时间间隔（10-300秒）

## 常见问题

### Q: 悬浮图标不显示？
A: 检查当前网站是否在代理站点列表中，确保插件已正确安装并启用。

### Q: 无法连接服务器？
A: 检查服务器地址是否正确，服务器是否正常运行，网络连接是否正常。

### Q: 登录状态检测不准确？
A: 可能是Cookie名称不在预设列表中，可以在设置中添加自定义Cookie名称。

### Q: 任务提交失败？
A: 检查文件格式是否正确，服务器是否正常，网络连接是否稳定。

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 实现基础的悬浮图标和助手面板
- 支持任务管理、智能对话、系统设置
- 实现Cookie自动采集和登录状态检测
- 支持WebSocket实时通信

## 技术支持

如有问题或建议，请联系开发团队或提交Issue。

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。
