// B端智能助手-APA 内容脚本
class ContentAssistant {
  constructor() {
    this.floatingIcon = null;
    this.assistantPanel = null;
    this.isVisible = false;
    this.connectionStatus = 'disconnected';
    this.init();
  }

  init() {
    // 监听来自background的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
    });

    // 检查当前域名是否支持
    this.checkDomainSupport();

    // 获取连接状态
    this.getConnectionStatus();
  }

  async checkDomainSupport() {
    const domain = window.location.hostname;
    // 这里可以添加域名检查逻辑
    // 暂时显示悬浮图标用于测试
    this.createFloatingIcon();
  }

  async getConnectionStatus() {
    chrome.runtime.sendMessage(
      { type: 'GET_CONNECTION_STATUS' },
      (response) => {
        if (response) {
          this.connectionStatus = response.status;
          this.updateConnectionStatus();
        }
      }
    );
  }

  handleMessage(request, sender, sendResponse) {
    switch (request.type) {
      case 'SHOW_FLOATING_ICON':
        this.createFloatingIcon();
        break;
      case 'CONNECTION_STATUS_UPDATE':
        this.connectionStatus = request.status;
        this.updateConnectionStatus();
        break;
      case 'CHAT_MESSAGE':
        this.handleChatMessage(request.message);
        break;
      case 'TASK_UPDATE':
        this.handleTaskUpdate(request.task);
        break;
    }
  }

  createFloatingIcon() {
    if (this.floatingIcon) return;

    // 创建悬浮图标容器
    this.floatingIcon = document.createElement('div');
    this.floatingIcon.id = 'apa-floating-icon';
    this.floatingIcon.innerHTML = `
      <div class="apa-icon-container">
        <div class="apa-robot-avatar">
          <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
            <circle cx="20" cy="20" r="18" fill="#4A90E2" stroke="#fff" stroke-width="2"/>
            <circle cx="15" cy="16" r="2" fill="#fff"/>
            <circle cx="25" cy="16" r="2" fill="#fff"/>
            <path d="M14 26 Q20 30 26 26" stroke="#fff" stroke-width="2" fill="none" stroke-linecap="round"/>
          </svg>
        </div>
        <div class="apa-status-indicator ${this.getStatusClass()}"></div>
      </div>
    `;

    // 添加样式
    this.addFloatingIconStyles();

    // 添加点击事件
    this.floatingIcon.addEventListener('click', () => {
      this.toggleAssistantPanel();
    });

    // 添加到页面
    document.body.appendChild(this.floatingIcon);

    // 添加拖拽功能
    this.makeDraggable(this.floatingIcon);
  }

  addFloatingIconStyles() {
    if (document.getElementById('apa-floating-styles')) return;

    const style = document.createElement('style');
    style.id = 'apa-floating-styles';
    style.textContent = `
      #apa-floating-icon {
        position: fixed;
        top: 50%;
        right: 20px;
        transform: translateY(-50%);
        z-index: 10000;
        cursor: pointer;
        user-select: none;
        transition: all 0.3s ease;
      }

      #apa-floating-icon:hover {
        transform: translateY(-50%) scale(1.1);
      }

      .apa-icon-container {
        position: relative;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #4A90E2, #357ABD);
        box-shadow: 0 4px 20px rgba(74, 144, 226, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
      }

      .apa-robot-avatar {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .apa-status-indicator {
        position: absolute;
        top: -2px;
        right: -2px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid #fff;
      }

      .apa-status-connected {
        background-color: #4CAF50;
      }

      .apa-status-connecting {
        background-color: #FF9800;
        animation: pulse 1.5s infinite;
      }

      .apa-status-disconnected {
        background-color: #F44336;
      }

      .apa-status-error {
        background-color: #F44336;
        animation: blink 1s infinite;
      }

      @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
      }

      @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0; }
      }

      #apa-assistant-panel {
        position: fixed;
        top: 50%;
        right: 80px;
        transform: translateY(-50%);
        width: 380px;
        height: 600px;
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        z-index: 10001;
        display: none;
        flex-direction: column;
        overflow: hidden;
        border: 1px solid #e0e0e0;
      }

      #apa-assistant-panel.visible {
        display: flex;
        animation: slideIn 0.3s ease-out;
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(-50%) translateX(20px);
        }
        to {
          opacity: 1;
          transform: translateY(-50%) translateX(0);
        }
      }
    `;

    document.head.appendChild(style);
  }

  getStatusClass() {
    switch (this.connectionStatus) {
      case 'connected': return 'apa-status-connected';
      case 'connecting': return 'apa-status-connecting';
      case 'disconnected': return 'apa-status-disconnected';
      case 'error': return 'apa-status-error';
      default: return 'apa-status-disconnected';
    }
  }

  updateConnectionStatus() {
    if (!this.floatingIcon) return;

    const indicator = this.floatingIcon.querySelector('.apa-status-indicator');
    if (indicator) {
      indicator.className = `apa-status-indicator ${this.getStatusClass()}`;
    }

    // 更新面板中的连接状态
    if (this.assistantPanel) {
      const statusElement = this.assistantPanel.querySelector('.connection-status');
      if (statusElement) {
        statusElement.textContent = this.getStatusText();
        statusElement.className = `connection-status ${this.getStatusClass()}`;
      }
    }
  }

  getStatusText() {
    switch (this.connectionStatus) {
      case 'connected': return '已连接';
      case 'connecting': return '连接中';
      case 'disconnected': return '未连接';
      case 'error': return '重试';
      default: return '未连接';
    }
  }

  toggleAssistantPanel() {
    if (!this.assistantPanel) {
      this.createAssistantPanel();
    }

    this.isVisible = !this.isVisible;
    if (this.isVisible) {
      this.assistantPanel.classList.add('visible');
    } else {
      this.assistantPanel.classList.remove('visible');
    }
  }

  createAssistantPanel() {
    this.assistantPanel = document.createElement('div');
    this.assistantPanel.id = 'apa-assistant-panel';

    // 创建面板内容
    this.assistantPanel.innerHTML = `
      <div class="apa-panel-header">
        <div class="apa-header-left">
          <span class="apa-title">智能助手</span>
          <span class="login-status">检查中...</span>
        </div>
        <div class="apa-header-right">
          <span class="connection-status ${this.getStatusClass()}">${this.getStatusText()}</span>
        </div>
      </div>
      <div class="apa-panel-tabs">
        <button class="apa-tab active" data-tab="tasks">辅助任务</button>
        <button class="apa-tab" data-tab="chat">智能对话</button>
        <button class="apa-tab" data-tab="settings">设置</button>
      </div>
      <div class="apa-panel-content">
        <div id="apa-tasks-content" class="apa-tab-content active">
          <div class="loading">加载中...</div>
        </div>
        <div id="apa-chat-content" class="apa-tab-content">
          <div class="loading">加载中...</div>
        </div>
        <div id="apa-settings-content" class="apa-tab-content">
          <div class="loading">加载中...</div>
        </div>
      </div>
    `;

    // 添加面板样式
    this.addPanelStyles();

    // 添加事件监听
    this.addPanelEventListeners();

    // 添加到页面
    document.body.appendChild(this.assistantPanel);

    // 检查登录状态
    this.checkLoginStatus();

    // 加载各个tab的内容
    this.loadTabContent('tasks');
  }

  addPanelStyles() {
    if (document.getElementById('apa-panel-styles')) return;

    const style = document.createElement('style');
    style.id = 'apa-panel-styles';
    style.textContent = `
      .apa-panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        background: #f8f9fa;
        border-bottom: 1px solid #e0e0e0;
      }

      .apa-header-left {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .apa-title {
        font-weight: 600;
        color: #333;
        font-size: 16px;
      }

      .login-status {
        font-size: 12px;
        color: #666;
        padding: 2px 8px;
        background: #e9ecef;
        border-radius: 12px;
      }

      .connection-status {
        font-size: 12px;
        padding: 4px 8px;
        border-radius: 12px;
        color: #fff;
        font-weight: 500;
      }

      .apa-panel-tabs {
        display: flex;
        background: #fff;
        border-bottom: 1px solid #e0e0e0;
      }

      .apa-tab {
        flex: 1;
        padding: 12px 16px;
        border: none;
        background: none;
        cursor: pointer;
        font-size: 14px;
        color: #666;
        transition: all 0.2s ease;
        border-bottom: 2px solid transparent;
      }

      .apa-tab:hover {
        background: #f8f9fa;
        color: #333;
      }

      .apa-tab.active {
        color: #4A90E2;
        border-bottom-color: #4A90E2;
        background: #fff;
      }

      .apa-panel-content {
        flex: 1;
        overflow: hidden;
        position: relative;
      }

      .apa-tab-content {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 16px;
        overflow-y: auto;
        display: none;
      }

      .apa-tab-content.active {
        display: block;
      }

      .loading {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #666;
        font-size: 14px;
      }
    `;

    document.head.appendChild(style);
  }

  addPanelEventListeners() {
    // Tab切换
    const tabs = this.assistantPanel.querySelectorAll('.apa-tab');
    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        const tabName = tab.dataset.tab;
        this.switchTab(tabName);
      });
    });
  }

  switchTab(tabName) {
    // 更新tab状态
    const tabs = this.assistantPanel.querySelectorAll('.apa-tab');
    tabs.forEach(tab => {
      tab.classList.toggle('active', tab.dataset.tab === tabName);
    });

    // 更新内容区域
    const contents = this.assistantPanel.querySelectorAll('.apa-tab-content');
    contents.forEach(content => {
      content.classList.toggle('active', content.id === `apa-${tabName}-content`);
    });

    // 加载对应内容
    this.loadTabContent(tabName);
  }

  loadTabContent(tabName) {
    const contentElement = this.assistantPanel.querySelector(`#apa-${tabName}-content`);
    if (!contentElement) return;

    // 动态加载对应的组件
    switch (tabName) {
      case 'tasks':
        this.loadTasksComponent(contentElement);
        break;
      case 'chat':
        this.loadChatComponent(contentElement);
        break;
      case 'settings':
        this.loadSettingsComponent(contentElement);
        break;
    }
  }

  async checkLoginStatus() {
    const domain = window.location.hostname;
    chrome.runtime.sendMessage(
      { type: 'CHECK_LOGIN_STATUS', domain: domain },
      (response) => {
        if (response && response.success) {
          const statusElement = this.assistantPanel.querySelector('.login-status');
          if (statusElement) {
            statusElement.textContent = response.isLoggedIn ? '已登录' : '未获取到登录态';
            statusElement.style.cursor = response.isLoggedIn ? 'default' : 'pointer';

            if (!response.isLoggedIn) {
              statusElement.addEventListener('click', () => {
                // 跳转到SSO登录页面
                this.redirectToSSO();
              });
            }
          }
        }
      }
    );
  }

  redirectToSSO() {
    // 这里需要根据配置的SSO地址进行跳转
    console.log('跳转到SSO登录页面');
  }

  makeDraggable(element) {
    let isDragging = false;
    let startX, startY, startLeft, startTop;

    element.addEventListener('mousedown', (e) => {
      isDragging = true;
      startX = e.clientX;
      startY = e.clientY;
      startLeft = parseInt(window.getComputedStyle(element).right);
      startTop = parseInt(window.getComputedStyle(element).top);

      element.style.cursor = 'grabbing';
      e.preventDefault();
    });

    document.addEventListener('mousemove', (e) => {
      if (!isDragging) return;

      const deltaX = startX - e.clientX;
      const deltaY = e.clientY - startY;

      element.style.right = (startLeft + deltaX) + 'px';
      element.style.top = (startTop + deltaY) + 'px';
      element.style.transform = 'none';
    });

    document.addEventListener('mouseup', () => {
      if (isDragging) {
        isDragging = false;
        element.style.cursor = 'pointer';
      }
    });
  }

  // 加载任务组件
  loadTasksComponent(container) {
    try {
      if (typeof TaskAssistant !== 'undefined') {
        new TaskAssistant(container);
      } else {
        this.loadScript('components/task-assistant.js').then(() => {
          new TaskAssistant(container);
        }).catch(() => {
          container.innerHTML = '<div class="error">任务组件加载失败</div>';
        });
      }
    } catch (error) {
      console.error('加载任务组件失败:', error);
      container.innerHTML = '<div class="error">任务组件加载失败</div>';
    }
  }

  // 加载聊天组件
  loadChatComponent(container) {
    try {
      if (typeof ChatComponent !== 'undefined') {
        new ChatComponent(container);
      } else {
        this.loadScript('components/chat.js').then(() => {
          new ChatComponent(container);
        }).catch(() => {
          container.innerHTML = '<div class="error">聊天组件加载失败</div>';
        });
      }
    } catch (error) {
      console.error('加载聊天组件失败:', error);
      container.innerHTML = '<div class="error">聊天组件加载失败</div>';
    }
  }

  // 加载设置组件
  loadSettingsComponent(container) {
    try {
      if (typeof SettingsComponent !== 'undefined') {
        new SettingsComponent(container);
      } else {
        this.loadScript('components/settings.js').then(() => {
          new SettingsComponent(container);
        }).catch(() => {
          container.innerHTML = '<div class="error">设置组件加载失败</div>';
        });
      }
    } catch (error) {
      console.error('加载设置组件失败:', error);
      container.innerHTML = '<div class="error">设置组件加载失败</div>';
    }
  }

  // 动态加载脚本
  loadScript(src) {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = chrome.runtime.getURL(src);
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  handleChatMessage(message) {
    // 处理聊天消息
    console.log('收到聊天消息:', message);
  }

  handleTaskUpdate(task) {
    // 处理任务更新
    console.log('任务更新:', task);
  }
}

// 初始化内容助手
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new ContentAssistant();
  });
} else {
  new ContentAssistant();
}
